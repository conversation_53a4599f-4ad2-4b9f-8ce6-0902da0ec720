#!/usr/bin/env python3
"""
PyQt6版本的续杯工具启动脚本
"""

import sys
import os
import warnings

# 设置环境变量减少Qt警告
os.environ['QT_AUTO_SCREEN_SCALE_FACTOR'] = '0'
os.environ['QT_SCALE_FACTOR'] = '1'
os.environ['QT_ENABLE_HIGHDPI_SCALING'] = '0'

# 忽略一些常见的警告
warnings.filterwarnings("ignore", category=DeprecationWarning)

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def main():
    """主函数"""
    print("🔑 续杯工具 - PyQt6版本")
    print("正在启动程序...")

    try:
        from 主程序 import create_gui

        # 启动GUI
        exit_code = create_gui()
        sys.exit(exit_code)

    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保已安装所有依赖包:")
        print("pip install -r requirements.txt")
        input("按回车键退出...")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 启动错误: {e}")
        input("按回车键退出...")
        sys.exit(1)

if __name__ == "__main__":
    main()

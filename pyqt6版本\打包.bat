@echo off
chcp 65001 >nul
title 续杯工具 - PyQt6版本打包脚本

echo ========================================
echo 续杯工具 - PyQt6版本打包脚本
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.8或更高版本
    pause
    exit /b 1
)

echo 正在检查PyInstaller...
python -c "import PyInstaller" >nul 2>&1
if errorlevel 1 (
    echo 正在安装PyInstaller...
    pip install pyinstaller
    if errorlevel 1 (
        echo 错误: PyInstaller安装失败
        pause
        exit /b 1
    )
)

echo 正在检查依赖包...
pip install -r requirements.txt
if errorlevel 1 (
    echo 警告: 部分依赖包安装失败，但继续打包...
)

echo.
echo 开始打包程序...
echo 这可能需要几分钟时间，请耐心等待...
echo.

pyinstaller build.spec

if errorlevel 1 (
    echo.
    echo 打包失败，请检查错误信息
    pause
    exit /b 1
)

echo.
echo ========================================
echo 打包完成！
echo ========================================
echo.
echo 可执行文件位置: dist\续杯工具.exe
echo.
echo 注意事项:
echo 1. 首次运行可能需要较长时间
echo 2. 确保目标机器有Chrome浏览器
echo 3. 程序会自动创建配置文件
echo.
pause

🔑 续杯工具 - PyQt6版本 (Windows专用)
========================================

📋 快速开始指南：

1️⃣ 安装Python
   - 下载并安装Python 3.8或更高版本
   - 下载地址：https://www.python.org/downloads/

2️⃣ 运行程序
   - 双击 "启动程序.bat" 文件
   - 程序会自动检查并安装依赖包

3️⃣ 配置邮箱
   - 在"邮箱管理中心"输入邮箱前缀和密码
   - 点击"保存凭据"

4️⃣ 设置浏览器
   - 在"浏览器设置"中选择Chrome浏览器路径
   - 或使用默认路径

5️⃣ 开始使用
   - 点击"一键登录"或"一键重置环境"
   - 查看右侧日志了解操作状态

🎯 主要功能：
   - 🔑 一键登录
   - 🔄 环境重置
   - 📧 邮箱管理
   - 🌐 浏览器设置
   - 📊 实时日志

⚠️ 注意事项：
   - 仅支持Windows系统
   - 需要Python 3.8+
   - 首次运行会自动安装依赖包

❓ 遇到问题？
   - 运行 test_import.py 检查环境
   - 查看控制台错误信息
   - 确保网络连接正常

🎨 界面特色：
   - 现代化设计
   - 响应式布局
   - 实时状态更新
   - 自动复制验证码

========================================
享受更美观的续杯体验！ 🎉

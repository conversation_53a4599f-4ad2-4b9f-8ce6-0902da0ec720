# 🔐 注册表配置系统说明

## 📋 功能概述

注册表配置系统将账户信息存储在Windows注册表中，提供更好的安全性和系统集成。相比传统的配置文件或内置配置，注册表存储具有更高的安全性和更好的系统兼容性。

## ✅ 优势特性

### 🔒 安全性优势
1. **系统级安全**：利用Windows注册表的内置安全机制
2. **用户隔离**：每个用户的配置独立存储，互不干扰
3. **权限控制**：受Windows用户权限系统保护
4. **难以篡改**：比普通文件更难被恶意修改

### 🚀 功能优势
1. **完全独立**：程序不依赖外部配置文件
2. **系统集成**：与Windows系统深度集成
3. **多用户支持**：支持Windows多用户环境
4. **持久存储**：配置信息永久保存在系统中

### 💡 用户体验
1. **一次设置**：配置一次，永久有效
2. **无文件依赖**：不会因配置文件丢失而失效
3. **简化部署**：只需一个exe文件即可运行
4. **便于管理**：可通过工具统一管理配置

## 🔧 使用方法

### 设置配置

1. **运行配置工具**：
   ```bash
   python 注册表配置管理.py
   ```

2. **输入配置信息**：
   - 邮箱前缀：输入 @2925.com 之前的部分
   - 邮箱密码：输入对应的密码

3. **确认设置**：
   - 确认配置信息无误后，输入 `y` 确认

4. **重新编译**：
   ```bash
   python build.py
   ```

### 查看配置

查看当前注册表配置：
```bash
python 注册表配置管理.py show
```

### 删除配置

删除注册表中的配置：
```bash
python 注册表配置管理.py delete
```

## 📍 注册表位置

配置信息存储在以下注册表位置：
```
HKEY_CURRENT_USER\SOFTWARE\CursorVipTool
```

### 存储的值
- `EmailPrefix`：Base64编码的邮箱前缀
- `EmailPassword`：Base64编码的邮箱密码
- `ConfigVersion`：配置版本号（用于兼容性）

## ⚙️ 配置优先级

程序按以下优先级加载配置：

1. **注册表配置**（最高优先级）
2. **外部配置文件**（Documents/.cursor-free-vip/config.ini）

当注册表中存在配置时，程序将忽略外部配置文件中的邮箱设置。

## 🛠️ 高级操作

### 手动查看注册表

1. 按 `Win + R` 打开运行对话框
2. 输入 `regedit` 并回车
3. 导航到：`HKEY_CURRENT_USER\SOFTWARE\CursorVipTool`
4. 查看存储的配置值

### 手动删除配置

如果工具无法删除配置，可以手动删除：
1. 在注册表编辑器中找到 `CursorVipTool` 键
2. 右键点击并选择"删除"
3. 确认删除操作

### 备份和恢复

**备份配置**：
1. 在注册表编辑器中右键点击 `CursorVipTool` 键
2. 选择"导出"
3. 保存为 `.reg` 文件

**恢复配置**：
1. 双击 `.reg` 文件
2. 确认导入操作

## 🔍 故障排除

### 常见问题

**Q: 提示"此工具仅支持Windows系统"**
A: 注册表功能仅在Windows系统中可用，其他系统请使用配置文件方式。

**Q: 无法保存到注册表**
A: 检查是否有足够的权限，尝试以管理员身份运行。

**Q: 程序无法读取注册表配置**
A: 确认配置已正确保存，可以使用工具查看当前配置状态。

**Q: 如何在不同电脑间同步配置**
A: 可以导出注册表配置文件，在其他电脑上导入。

### 调试步骤

1. **检查配置状态**：
   ```bash
   python 注册表配置管理.py show
   ```

2. **重新设置配置**：
   ```bash
   python 注册表配置管理.py
   ```

3. **检查程序日志**：查看程序运行时的状态信息

## 📝 示例操作

### 完整设置流程

假设您的邮箱是 `<EMAIL>`，密码是 `mypassword`：

1. **设置配置**：
   ```bash
   python 注册表配置管理.py
   # 输入：test123
   # 输入：mypassword
   # 确认：y
   ```

2. **验证配置**：
   ```bash
   python 注册表配置管理.py show
   # 输出：邮箱前缀: test123
   ```

3. **编译程序**：
   ```bash
   python build.py
   ```

4. **运行程序**：程序将自动使用注册表中的配置

## 🔄 迁移指南

### 从内置配置迁移

如果您之前使用内置配置，可以这样迁移：

1. 查看当前内置配置（如果有）
2. 使用注册表工具设置相同的配置
3. 重新编译程序
4. 新程序将优先使用注册表配置

### 从配置文件迁移

如果您之前使用外部配置文件：

1. 查看配置文件中的邮箱设置
2. 使用注册表工具设置相同的配置
3. 重新编译程序
4. 配置文件将被注册表配置覆盖

## 🎯 最佳实践

1. **定期备份**：定期导出注册表配置作为备份
2. **权限管理**：确保只有授权用户可以修改配置
3. **版本控制**：为不同环境维护不同的配置
4. **安全考虑**：虽然比文件安全，但仍建议定期更换密码

---

现在您可以享受更安全、更稳定的配置管理体验！🚀

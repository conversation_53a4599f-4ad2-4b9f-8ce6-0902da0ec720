# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['主程序.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('drivers', 'drivers'),
        ('requirements.txt', '.'),
        ('README.md', '.'),
        ('快速开始.txt', '.'),
    ],
    hiddenimports=[
        'PyQt6.QtCore',
        'PyQt6.QtGui', 
        'PyQt6.QtWidgets',
        'bs4',
        'lxml',
        'pywinauto',
        'DrissionPage',
        'configparser',
        'poplib',
        'email',
        'multiprocessing',
        'threading',
        'queue',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='续杯工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)

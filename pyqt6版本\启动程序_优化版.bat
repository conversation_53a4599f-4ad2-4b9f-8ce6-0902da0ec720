@echo off
chcp 65001 >nul
title 续杯工具 - PyQt6版本 (Windows专用)

REM 设置环境变量减少Qt警告
set QT_AUTO_SCREEN_SCALE_FACTOR=0
set QT_SCALE_FACTOR=1
set QT_ENABLE_HIGHDPI_SCALING=0

echo ========================================
echo 续杯工具 - PyQt6版本 (Windows专用)
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo 正在检查依赖包...
python -c "import PyQt6" >nul 2>&1
if errorlevel 1 (
    echo 警告: 未找到PyQt6，正在尝试安装依赖包...
    echo 这可能需要几分钟时间，请耐心等待...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 依赖包安装失败
        echo 请尝试手动安装: pip install PyQt6
        pause
        exit /b 1
    )
    echo 依赖包安装完成！
)

echo 正在启动程序...
echo 注意: 启动时可能会出现一些Qt警告信息，这是正常现象，不影响程序运行。
echo.
python 主程序.py 2>nul

if errorlevel 1 (
    echo.
    echo 程序运行出错，正在显示详细错误信息...
    python 主程序.py
    echo.
    echo 如果问题持续存在，请尝试运行: python test_import.py
    pause
)

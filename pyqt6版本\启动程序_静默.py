#!/usr/bin/env python3
"""
PyQt6版本的续杯工具静默启动脚本
减少Qt相关的警告信息
"""

import sys
import os
import warnings
import subprocess

# 设置环境变量减少Qt警告
os.environ['QT_AUTO_SCREEN_SCALE_FACTOR'] = '0'
os.environ['QT_SCALE_FACTOR'] = '1'
os.environ['QT_ENABLE_HIGHDPI_SCALING'] = '0'
os.environ['QT_LOGGING_RULES'] = '*.debug=false;qt.qpa.*=false'

# 忽略警告
warnings.filterwarnings("ignore")

# 重定向stderr来隐藏Qt警告
class NullWriter:
    def write(self, txt): pass
    def flush(self): pass

def main():
    """主函数"""
    print("🔑 续杯工具 - PyQt6版本 (静默启动)")
    print("正在启动程序...")
    
    # 添加当前目录到Python路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    sys.path.insert(0, current_dir)
    
    try:
        # 临时重定向stderr来隐藏Qt警告
        original_stderr = sys.stderr
        sys.stderr = NullWriter()
        
        from 主程序 import create_gui
        
        # 恢复stderr
        sys.stderr = original_stderr
        
        print("✅ 程序启动成功！")
        
        # 启动GUI
        exit_code = create_gui()
        sys.exit(exit_code)
        
    except ImportError as e:
        # 恢复stderr
        sys.stderr = original_stderr
        print(f"❌ 导入错误: {e}")
        print("请确保已安装所有依赖包:")
        print("pip install -r requirements.txt")
        input("按回车键退出...")
        sys.exit(1)
    except Exception as e:
        # 恢复stderr
        sys.stderr = original_stderr
        print(f"❌ 启动错误: {e}")
        input("按回车键退出...")
        sys.exit(1)

if __name__ == "__main__":
    main()

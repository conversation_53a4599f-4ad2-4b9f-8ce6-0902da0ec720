import os
import sys
import configparser
from colorama import Fore, Style
import shutil
import datetime
import platform
import random
import base64

# ==================== 注册表配置 ====================
# 将账户信息存储在Windows注册表中，提供更好的安全性和系统集成
# 使用Base64编码存储，并利用Windows注册表的安全机制

# 注册表配置路径
REGISTRY_KEY_PATH = r"SOFTWARE\CursorVipTool"
REGISTRY_VALUES = {
    'email_prefix': 'EmailPrefix',
    'email_password': 'EmailPassword',
    'config_version': 'ConfigVersion'
}

def get_registry_email_config():
    """从Windows注册表获取邮箱配置"""
    if platform.system() != "Windows":
        return None

    try:
        import winreg

        # 尝试打开注册表键
        try:
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, REGISTRY_KEY_PATH) as key:
                # 读取邮箱前缀
                try:
                    encoded_prefix, _ = winreg.QueryValueEx(key, REGISTRY_VALUES['email_prefix'])
                    prefix = base64.b64decode(encoded_prefix).decode()
                except FileNotFoundError:
                    return None

                # 读取邮箱密码
                try:
                    encoded_password, _ = winreg.QueryValueEx(key, REGISTRY_VALUES['email_password'])
                    password = base64.b64decode(encoded_password).decode()
                except FileNotFoundError:
                    return None

                return {
                    'prefix': prefix,
                    'password': password
                }
        except FileNotFoundError:
            # 注册表键不存在
            return None

    except Exception as e:
        safe_log(f"读取注册表配置失败: {e}", 'error')
        return None

def set_registry_email_config(prefix, password):
    """将邮箱配置保存到Windows注册表"""
    if platform.system() != "Windows":
        return False

    try:
        import winreg

        # 编码配置信息
        encoded_prefix = base64.b64encode(prefix.encode()).decode()
        encoded_password = base64.b64encode(password.encode()).decode()

        # 创建或打开注册表键
        with winreg.CreateKey(winreg.HKEY_CURRENT_USER, REGISTRY_KEY_PATH) as key:
            # 设置邮箱前缀
            winreg.SetValueEx(key, REGISTRY_VALUES['email_prefix'], 0, winreg.REG_SZ, encoded_prefix)

            # 设置邮箱密码
            winreg.SetValueEx(key, REGISTRY_VALUES['email_password'], 0, winreg.REG_SZ, encoded_password)

            # 设置配置版本（用于将来的兼容性）
            winreg.SetValueEx(key, REGISTRY_VALUES['config_version'], 0, winreg.REG_SZ, "1.0")

        safe_log("邮箱配置已保存到注册表")
        return True

    except Exception as e:
        safe_log(f"保存注册表配置失败: {e}", 'error')
        return False

def delete_registry_email_config():
    """从Windows注册表删除邮箱配置"""
    if platform.system() != "Windows":
        return False

    try:
        import winreg

        # 尝试删除注册表键
        try:
            winreg.DeleteKey(winreg.HKEY_CURRENT_USER, REGISTRY_KEY_PATH)
            safe_log("注册表配置已删除")
            return True
        except FileNotFoundError:
            # 键不存在，认为删除成功
            return True

    except Exception as e:
        safe_log(f"删除注册表配置失败: {e}", 'error')
        return False

def check_registry_config_exists():
    """检查注册表中是否存在配置"""
    if platform.system() != "Windows":
        return False

    try:
        import winreg

        with winreg.OpenKey(winreg.HKEY_CURRENT_USER, REGISTRY_KEY_PATH) as key:
            # 检查必要的值是否存在
            winreg.QueryValueEx(key, REGISTRY_VALUES['email_prefix'])
            winreg.QueryValueEx(key, REGISTRY_VALUES['email_password'])
            return True
    except (FileNotFoundError, OSError):
        return False

# ==================== 原有配置系统 ====================

# 从主程序导入需要的函数和常量
try:
    from 主程序 import get_user_documents_path, get_linux_cursor_path, get_default_driver_path, get_default_browser_path, EMOJI
except ImportError:
    # 如果主程序未导入，定义基本的EMOJI常量
    EMOJI = {
        "INFO": "ℹ️",
        "ERROR": "❌",
        "SUCCESS": "✅",
        "WARNING": "⚠️"
    }

    def get_user_documents_path():
        """Get user documents path"""
        if platform.system() == "Windows":
            try:
                import winreg
                with winreg.OpenKey(winreg.HKEY_CURRENT_USER, "Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Shell Folders") as key:
                    documents_path, _ = winreg.QueryValueEx(key, "Personal")
                    return documents_path
            except Exception as e:
                return os.path.expanduser("~\\Documents")
        else:
            return os.path.expanduser("~/Documents")

    def get_default_driver_path(browser_type='chrome'):
        """Get default driver path for Chrome."""
        if sys.platform == "win32":
            return os.path.join(os.path.dirname(os.path.abspath(__file__)), "drivers", "chromedriver.exe")
        elif sys.platform == "darwin":
            return os.path.join(os.path.dirname(os.path.abspath(__file__)), "drivers", "chromedriver")
        else:
            return "/usr/local/bin/chromedriver"

    def get_default_browser_path(browser_type='chrome'):
        """Get default Chrome browser executable path"""
        if sys.platform == "win32":
            return r"C:\Program Files\Google\Chrome\Application\chrome.exe"
        elif sys.platform == "darwin":
            return "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
        else:
            return "/usr/bin/google-chrome"

    def get_linux_cursor_path():
        """Get Linux Cursor path"""
        possible_paths = [
            "/opt/Cursor/resources/app",
            "/usr/share/cursor/resources/app",
            "/opt/cursor-bin/resources/app",
            "/usr/lib/cursor/resources/app",
            os.path.expanduser("~/.local/share/cursor/resources/app")
        ]
        return next((path for path in possible_paths if os.path.exists(path)), possible_paths[0])

# global config cache
_config_cache = None

def safe_log(message, level='info'):
    """安全的日志记录函数，用于GUI应用 - 现在不记录到文件"""
    # 不再记录日志到文件，静默处理
    pass

def setup_config():
    """Setup configuration file and return config object"""
    try:
        # get documents path
        docs_path = get_user_documents_path()
        if not docs_path or not os.path.exists(docs_path):
            # if documents path not found, use current directory
            safe_log("未找到文档路径，使用当前目录", 'warning')
            docs_path = os.path.abspath('.')

        # normalize path
        config_dir = os.path.normpath(os.path.join(docs_path, ".cursor-free-vip"))
        config_file = os.path.normpath(os.path.join(config_dir, "config.ini"))

        # create config directory, only print message when directory not exists
        dir_exists = os.path.exists(config_dir)
        try:
            os.makedirs(config_dir, exist_ok=True)
            if not dir_exists:  # only log message when directory not exists
                safe_log(f"配置目录已创建: {config_dir}")
        except Exception as e:
            # if cannot create directory, use temporary directory
            import tempfile
            temp_dir = os.path.normpath(os.path.join(tempfile.gettempdir(), ".cursor-free-vip"))
            temp_exists = os.path.exists(temp_dir)
            config_dir = temp_dir
            config_file = os.path.normpath(os.path.join(config_dir, "config.ini"))
            os.makedirs(config_dir, exist_ok=True)
            if not temp_exists:  # only log message when temporary directory not exists
                safe_log(f"因错误使用临时目录: {config_dir} (错误: {str(e)})", 'warning')

        # create config object
        config = configparser.ConfigParser()

        # Dynamically construct the user-specific Chrome path
        user_chrome_path = ''
        if sys.platform == "win32":
            local_app_data = os.getenv('LOCALAPPDATA')
            if local_app_data:
                user_chrome_path = os.path.join(local_app_data, 'Google', 'Chrome', 'Application', 'chrome.exe')

        # Default configuration
        default_config = {
            'Browser': {
                'default_browser': 'chrome',
                'chrome_path': user_chrome_path if user_chrome_path and os.path.exists(user_chrome_path) else get_default_browser_path('chrome'),
                'chrome_driver_path': get_default_driver_path('chrome'),
            },
            'Turnstile': {
                'handle_turnstile_time': '2',
                'handle_turnstile_random_time': '1-3'
            },
            'Timing': {
                'min_random_time': '0.1',
                'max_random_time': '0.8',
                'page_load_wait': '0.1-0.8',
                'input_wait': '0.3-0.8',
                'submit_wait': '0.5-1.5',
                'verification_code_input': '0.1-0.3',
                'verification_success_wait': '2-3',
                'verification_retry_wait': '2-3',
                'email_check_initial_wait': '4-6',
                'email_refresh_wait': '2-4',
                'settings_page_load_wait': '1-2',
                'failed_retry_time': '0.5-1',
                'retry_interval': '8-12',
                'max_timeout': '160'
            },
            'Utils': {
                'enabled_update_check': 'True',
                'enabled_force_update': 'False',
                'enabled_account_info': 'True'
            },
            'OAuth': {
                'show_selection_alert': False,  # 默认不显示选择提示弹窗
                'timeout': 120,
                'max_attempts': 3
            },
            'Token': {
                'refresh_server': 'https://token.cursorpro.com.cn',
                'enable_refresh': True
            },
            'Email': {
                'prefix': '',
                'password': ''
            }
        }

        # Add system-specific path configuration
        if sys.platform == "win32":
            appdata = os.getenv("APPDATA") or ''
            localappdata = os.getenv("LOCALAPPDATA") or ''

            if not appdata or not localappdata:
                print(f"{Fore.RED}{EMOJI['ERROR']} APPDATA or LOCALAPPDATA environment variable not found.{Style.RESET_ALL}")
                # Fallback to a temp directory to avoid crashing
                import tempfile
                appdata = localappdata = tempfile.gettempdir()

            default_config['WindowsPaths'] = {
                'storage_path': os.path.join(appdata, "Cursor", "User", "globalStorage", "storage.json"),
                'sqlite_path': os.path.join(appdata, "Cursor", "User", "globalStorage", "state.vscdb"),
                'machine_id_path': os.path.join(appdata, "Cursor", "machineId"),
                'cursor_path': os.path.join(localappdata, "Programs", "Cursor", "resources", "app"),
                'updater_path': os.path.join(localappdata, "cursor-updater"),
                'update_yml_path': os.path.join(localappdata, "Programs", "Cursor", "resources", "app-update.yml"),
                'product_json_path': os.path.join(localappdata, "Programs", "Cursor", "resources", "app", "product.json")
            }
            # Create storage directory
            os.makedirs(os.path.dirname(default_config['WindowsPaths']['storage_path']), exist_ok=True)

        elif sys.platform == "darwin":
            default_config['MacPaths'] = {
                'storage_path': os.path.abspath(os.path.expanduser("~/Library/Application Support/Cursor/User/globalStorage/storage.json")),
                'sqlite_path': os.path.abspath(os.path.expanduser("~/Library/Application Support/Cursor/User/globalStorage/state.vscdb")),
                'machine_id_path': os.path.expanduser("~/Library/Application Support/Cursor/machineId"),
                'cursor_path': "/Applications/Cursor.app/Contents/Resources/app",
                'updater_path': os.path.expanduser("~/Library/Application Support/cursor-updater"),
                'update_yml_path': "/Applications/Cursor.app/Contents/Resources/app-update.yml",
                'product_json_path': "/Applications/Cursor.app/Contents/Resources/app/product.json"
            }
            # Create storage directory
            os.makedirs(os.path.dirname(default_config['MacPaths']['storage_path']), exist_ok=True)

        elif sys.platform == "linux":
            # Get the actual user's home directory, handling both sudo and normal cases
            sudo_user = os.environ.get('SUDO_USER')
            current_user = sudo_user if sudo_user else (os.getenv('USER') or os.getenv('USERNAME'))

            if not current_user:
                current_user = os.path.expanduser('~').split('/')[-1]

            # Handle sudo case
            if sudo_user:
                actual_home = f"/home/<USER>"
                root_home = "/root"
            else:
                actual_home = f"/home/<USER>"
                root_home = None

            if not os.path.exists(actual_home):
                actual_home = os.path.expanduser("~")

            # Define base config directory
            config_base = os.path.join(actual_home, ".config")

            # Try both "Cursor" and "cursor" directory names in both user and root locations
            cursor_dir = None
            possible_paths = [
                os.path.join(config_base, "Cursor"),
                os.path.join(config_base, "cursor"),
                os.path.join(root_home, ".config", "Cursor") if root_home else None,
                os.path.join(root_home, ".config", "cursor") if root_home else None
            ]

            for path in possible_paths:
                if path and os.path.exists(path):
                    cursor_dir = path
                    break

            if not cursor_dir:
                print(f"{Fore.YELLOW}{EMOJI['WARNING']} 在 {config_base} 中未找到 Cursor 或 cursor 目录{Style.RESET_ALL}")
                if root_home:
                    print(f"{Fore.YELLOW}{EMOJI['INFO']} 同时检查了 {root_home}/.config{Style.RESET_ALL}")
                print(f"{Fore.YELLOW}{EMOJI['INFO']} 请确保 Cursor 已安装并至少运行过一次{Style.RESET_ALL}")

            # Define Linux paths using the found cursor directory
            storage_path = os.path.abspath(os.path.join(cursor_dir, "User/globalStorage/storage.json")) if cursor_dir else ""
            storage_dir = os.path.dirname(storage_path) if storage_path else ""

            # Define all paths using the found cursor directory
            default_config['LinuxPaths'] = {
                'storage_path': storage_path,
                'sqlite_path': os.path.abspath(os.path.join(cursor_dir, "User/globalStorage/state.vscdb")) if cursor_dir else "",
                'machine_id_path': os.path.join(cursor_dir, "machineid") if cursor_dir else "",
                'cursor_path': get_linux_cursor_path(),
                'updater_path': os.path.join(config_base, "cursor-updater"),
                'update_yml_path': os.path.join(cursor_dir, "resources/app-update.yml") if cursor_dir else "",
                'product_json_path': os.path.join(cursor_dir, "resources/app/product.json") if cursor_dir else ""
            }

        # Add tempmail_plus configuration
        default_config['TempMailPlus'] = {
            'enabled': 'false',
            'email': '',
            'epin': ''
        }

        # Read existing configuration and merge
        if os.path.exists(config_file):
            config.read(config_file, encoding='utf-8')
            config_modified = False

            for section, options in default_config.items():
                if not config.has_section(section):
                    config.add_section(section)
                    config_modified = True
                for option, value in options.items():
                    if not config.has_option(section, option):
                        config.set(section, option, str(value))
                        config_modified = True
                        safe_log(f"已添加配置选项: {section}.{option}")

            # 应用邮箱配置（优先级：注册表 > 配置文件）
            registry_email = get_registry_email_config()
            if registry_email:
                if not config.has_section('Email'):
                    config.add_section('Email')
                    config_modified = True

                current_prefix = config.get('Email', 'prefix', fallback='')
                current_password = config.get('Email', 'password', fallback='')

                if current_prefix != registry_email['prefix']:
                    config.set('Email', 'prefix', registry_email['prefix'])
                    config_modified = True

                if current_password != registry_email['password']:
                    config.set('Email', 'password', registry_email['password'])
                    config_modified = True

                if config_modified:
                    safe_log("已从注册表更新邮箱配置")

            if config_modified:
                with open(config_file, 'w', encoding='utf-8') as f:
                    config.write(f)
                safe_log("配置已更新")
        else:
            for section, options in default_config.items():
                config.add_section(section)
                for option, value in options.items():
                    config.set(section, option, str(value))

            with open(config_file, 'w', encoding='utf-8') as f:
                config.write(f)
            safe_log(f"配置已创建: {config_file}")

        # 应用邮箱配置（优先级：注册表 > 内置配置）
        registry_email = get_registry_email_config()
        if registry_email:
            if not config.has_section('Email'):
                config.add_section('Email')
            config.set('Email', 'prefix', registry_email['prefix'])
            config.set('Email', 'password', registry_email['password'])
            safe_log("已应用注册表邮箱配置")
        else:
            # 如果注册表中没有配置，检查是否有内置配置（向后兼容）
            safe_log("注册表中未找到邮箱配置，建议使用注册表存储")

        return config

    except Exception as e:
        safe_log(f"设置配置时出错: {str(e)}", 'error')
        return None

def get_config():
    """Get existing config or create new one"""
    global _config_cache
    if _config_cache is None:
        _config_cache = setup_config()
    return _config_cache

def save_config(config):
    """Saves the provided config object to the config file."""
    try:
        # get documents path
        docs_path = get_user_documents_path()
        if not docs_path or not os.path.exists(docs_path):
            docs_path = os.path.abspath('.')

        config_dir = os.path.normpath(os.path.join(docs_path, ".cursor-free-vip"))
        config_file = os.path.normpath(os.path.join(config_dir, "config.ini"))

        with open(config_file, 'w', encoding='utf-8') as f:
            config.write(f)

        # Invalidate cache so it's re-read next time
        global _config_cache
        _config_cache = config

        return True
    except Exception as e:
        safe_log(f"保存配置时出错: {e}", 'error')
        return False

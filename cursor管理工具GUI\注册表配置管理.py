#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
注册表配置管理工具
用于在Windows注册表中管理账号密码，提供更好的安全性和系统集成
"""

import base64
import os
import sys
import platform

def check_windows():
    """检查是否为Windows系统"""
    if platform.system() != "Windows":
        print("❌ 此工具仅支持Windows系统")
        print("注册表功能仅在Windows系统中可用")
        return False
    return True

def set_registry_config(prefix, password):
    """设置注册表配置"""
    if not check_windows():
        return False

    try:
        from 配置管理 import set_registry_email_config

        success = set_registry_email_config(prefix, password)
        if success:
            print("✅ 邮箱配置已保存到注册表！")
            print(f"邮箱前缀: {prefix}")
            print(f"完整邮箱: {prefix}@2925.com")
            print(f"密码: {'*' * len(password)}")
            return True
        else:
            print("❌ 保存到注册表失败")
            return False

    except Exception as e:
        print(f"❌ 设置注册表配置失败: {e}")
        return False

def get_registry_config():
    """获取注册表配置"""
    if not check_windows():
        return None

    try:
        from 配置管理 import get_registry_email_config

        config = get_registry_email_config()
        if config:
            return config
        else:
            print("注册表中未找到邮箱配置")
            return None

    except Exception as e:
        print(f"❌ 读取注册表配置失败: {e}")
        return None

def delete_registry_config():
    """删除注册表配置"""
    if not check_windows():
        return False

    try:
        from 配置管理 import delete_registry_email_config

        success = delete_registry_email_config()
        if success:
            print("✅ 注册表配置已删除")
            return True
        else:
            print("❌ 删除注册表配置失败")
            return False

    except Exception as e:
        print(f"❌ 删除注册表配置失败: {e}")
        return False

def check_registry_exists():
    """检查注册表配置是否存在"""
    if not check_windows():
        return False

    try:
        from 配置管理 import check_registry_config_exists
        return check_registry_config_exists()
    except Exception as e:
        print(f"❌ 检查注册表配置失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 注册表配置管理工具")
    print("=" * 50)
    print("此工具将账号密码存储在Windows注册表中，提供更好的安全性和系统集成")
    print()

    if not check_windows():
        return

    # 检查当前配置状态
    if check_registry_exists():
        current_config = get_registry_config()
        if current_config:
            print("📋 当前注册表配置:")
            print(f"邮箱前缀: {current_config['prefix']}")
            print(f"完整邮箱: {current_config['prefix']}@2925.com")
            print(f"密码: {'*' * len(current_config['password'])}")
            print()

            action = input("选择操作 (1=更新配置, 2=删除配置, 其他=退出): ").strip()

            if action == "2":
                confirm = input("确认删除注册表配置？(y/N): ").strip().lower()
                if confirm in ['y', 'yes']:
                    if delete_registry_config():
                        print("配置已删除")
                    return
                else:
                    print("操作已取消")
                    return
            elif action != "1":
                print("操作已取消")
                return

    # 获取用户输入
    print("请输入邮箱配置信息：")
    prefix = input("邮箱前缀（@2925.com之前的部分）: ").strip()

    if not prefix:
        print("❌ 邮箱前缀不能为空")
        return

    password = input("邮箱密码: ").strip()

    if not password:
        print("❌ 邮箱密码不能为空")
        return

    # 确认信息
    print(f"\n📋 配置信息确认:")
    print(f"完整邮箱: {prefix}@2925.com")
    print(f"密码: {'*' * len(password)}")

    confirm = input("\n确认设置此配置？(y/N): ").strip().lower()

    if confirm not in ['y', 'yes']:
        print("❌ 操作已取消")
        return

    # 设置配置
    if set_registry_config(prefix, password):
        print("\n🎉 配置设置完成！")
        print("\n📋 下一步操作:")
        print("1. 运行 python build.py 重新编译程序")
        print("2. 新的可执行文件将自动使用注册表中的配置")
        print("3. 程序将优先使用注册表配置，无需外部配置文件")
        print("\n✅ 优势:")
        print("- 配置存储在系统注册表中，更安全")
        print("- 利用Windows系统的安全机制")
        print("- 程序完全独立，无外部依赖")
        print("- 支持多用户环境")
    else:
        print("❌ 配置设置失败")

def show_current_config():
    """显示当前注册表配置"""
    print("📋 注册表配置状态:")

    if not check_windows():
        return

    if check_registry_exists():
        config = get_registry_config()
        if config:
            print("✅ 注册表中存在邮箱配置")
            print(f"邮箱前缀: {config['prefix']}")
            print(f"完整邮箱: {config['prefix']}@2925.com")
            print(f"密码: {'*' * len(config['password'])}")
        else:
            print("❌ 无法读取注册表配置")
    else:
        print("❌ 注册表中未找到邮箱配置")
        print("请运行程序设置配置")

def delete_config():
    """删除配置"""
    print("🗑️ 删除注册表配置")

    if not check_windows():
        return

    if not check_registry_exists():
        print("注册表中未找到配置")
        return

    confirm = input("确认删除注册表中的邮箱配置？(y/N): ").strip().lower()
    if confirm in ['y', 'yes']:
        if delete_registry_config():
            print("✅ 配置已删除")
        else:
            print("❌ 删除失败")
    else:
        print("操作已取消")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        if sys.argv[1] == "show":
            show_current_config()
        elif sys.argv[1] == "delete":
            delete_config()
        else:
            print("用法:")
            print("  python 设置内置配置.py        # 设置配置")
            print("  python 设置内置配置.py show   # 显示配置")
            print("  python 设置内置配置.py delete # 删除配置")
    else:
        main()

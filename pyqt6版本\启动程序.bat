@echo off
chcp 65001 >nul
title 续杯工具 - PyQt6版本

echo ========================================
echo 续杯工具 - PyQt6版本
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.8或更高版本
    pause
    exit /b 1
)

echo 正在检查依赖包...
python -c "import PyQt6" >nul 2>&1
if errorlevel 1 (
    echo 警告: 未找到PyQt6，正在尝试安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 依赖包安装失败
        pause
        exit /b 1
    )
)

echo 正在启动程序...
echo.
python run.py

if errorlevel 1 (
    echo.
    echo 程序运行出错，请检查错误信息
    pause
)

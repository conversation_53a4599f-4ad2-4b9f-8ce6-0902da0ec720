# 🔐 内置配置功能说明

## 📋 功能概述

内置配置功能允许您将邮箱账号密码直接编码到程序中，使程序完全独立运行，不依赖任何外部配置文件。

## ✅ 优势

1. **完全独立**：程序不依赖外部配置文件，可以在任何环境下运行
2. **便于分发**：只需要一个exe文件，无需额外的配置文件
3. **避免丢失**：配置信息永久保存在程序内部，不会因为文件丢失而失效
4. **简化部署**：无需手动创建或修改配置文件

## 🔧 使用方法

### 方法一：使用配置工具（推荐）

1. **运行配置工具**：
   ```bash
   python 设置内置配置.py
   ```

2. **输入配置信息**：
   - 邮箱前缀：输入 @2925.com 之前的部分（如：s131995059）
   - 邮箱密码：输入对应的密码

3. **确认设置**：
   - 确认配置信息无误后，输入 `y` 确认

4. **重新编译**：
   ```bash
   python build.py
   ```

5. **完成**：新的 `续杯工具.exe` 将包含您的配置信息

### 方法二：手动修改代码

1. **打开配置文件**：编辑 `配置管理.py`

2. **找到内置配置部分**：
   ```python
   BUILTIN_EMAIL_CONFIG = {
       'prefix': base64.b64encode('您的邮箱前缀'.encode()).decode(),
       'password': base64.b64encode('您的密码'.encode()).decode(),
       'enabled': True
   }
   ```

3. **修改配置**：
   - 将 `您的邮箱前缀` 替换为实际的邮箱前缀
   - 将 `您的密码` 替换为实际的密码

4. **重新编译**：
   ```bash
   python build.py
   ```

## 📋 查看当前配置

查看当前内置配置状态：
```bash
python 设置内置配置.py show
```

输出示例：
```
📋 当前内置配置状态:
启用状态: 启用
邮箱前缀: s131995059
完整邮箱: <EMAIL>
密码: ***********
```

## ⚙️ 配置优先级

1. **内置配置启用时**：程序优先使用内置配置，忽略外部配置文件
2. **内置配置禁用时**：程序使用外部配置文件（`Documents/.cursor-free-vip/config.ini`）

## 🔒 安全说明

1. **编码方式**：使用 Base64 编码存储，提供基本混淆（不是安全加密）
2. **源码保护**：配置信息编译到exe文件中，不易被直接查看
3. **注意事项**：Base64 不是加密，有一定技术能力的人仍可能解码

## 🛠️ 高级操作

### 禁用内置配置

如需禁用内置配置，修改 `配置管理.py` 中的：
```python
BUILTIN_EMAIL_CONFIG = {
    # ...
    'enabled': False  # 改为 False
}
```

然后重新编译程序。

### 清空内置配置

将配置设置为空值：
```python
BUILTIN_EMAIL_CONFIG = {
    'prefix': '',
    'password': '',
    'enabled': False
}
```

## 📝 示例配置

假设您的邮箱是 `<EMAIL>`，密码是 `mypassword`：

1. **邮箱前缀**：`test123`
2. **密码**：`mypassword`

配置后，程序将自动使用 `<EMAIL>` 作为邮箱地址。

## ❓ 常见问题

**Q: 内置配置会覆盖外部配置吗？**
A: 是的，当内置配置启用时，会覆盖外部配置文件中的邮箱设置。

**Q: 如何在不同环境使用不同配置？**
A: 可以编译多个版本的exe文件，每个包含不同的内置配置。

**Q: 配置信息安全吗？**
A: Base64编码提供基本混淆，但不是安全加密。对于高安全要求，建议使用外部配置文件。

**Q: 可以同时使用内置配置和外部配置吗？**
A: 不可以，程序会优先使用内置配置。如需使用外部配置，请禁用内置配置。

## 🎯 最佳实践

1. **测试环境**：先在测试环境验证配置正确性
2. **备份源码**：修改配置前备份原始源码
3. **版本管理**：为不同配置创建不同版本的程序
4. **文档记录**：记录每个版本使用的配置信息

---

现在您可以享受完全独立的程序体验，无需担心配置文件丢失或环境依赖问题！🚀

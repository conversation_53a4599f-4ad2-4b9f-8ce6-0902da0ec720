# 续杯工具 PyQt6版本 - 项目说明

## 🎯 项目概述

本项目是原tkinter版本续杯工具的PyQt6重写版本，专为Windows系统优化，提供了更现代化、美观的用户界面。

## ✨ 主要改进

### 🎨 界面设计
- **现代化主题**: 采用蓝色主色调的现代化设计
- **响应式布局**: 自适应窗口大小变化
- **圆角设计**: 按钮和输入框采用圆角设计
- **状态指示**: 实时的状态指示器和进度反馈

### 🚀 性能优化
- **多线程处理**: 避免界面冻结，提供流畅体验
- **异常处理**: 完善的错误处理和用户提示
- **内存优化**: 优化了资源使用和内存管理

### 🔧 功能增强
- **自动复制**: 验证码自动复制到剪贴板
- **实时更新**: 随机邮箱每秒自动更新
- **配置管理**: 改进的配置保存和加载机制
- **Windows专用**: 移除跨平台代码，专为Windows优化

## 📁 文件结构

```
pyqt6版本/
├── 主程序.py              # 主程序文件，包含GUI界面
├── 配置管理.py            # 配置文件管理，注册表操作
├── 邮箱管理.py            # 邮箱功能，验证码监控
├── 应用管理.py            # Cursor应用管理，环境重置
├── drivers/               # 浏览器驱动文件夹
│   └── chromedriver.exe   # Chrome驱动程序
├── 启动程序.bat           # Windows批处理启动脚本
├── test_import.py         # 导入测试脚本
├── run.py                 # Python启动脚本
├── requirements.txt       # 依赖包列表
├── README.md              # 详细说明文档
├── 快速开始.txt           # 快速开始指南
└── 项目说明.md            # 本文件
```

## 🛠️ 技术栈

- **GUI框架**: PyQt6
- **Python版本**: 3.8+
- **主要依赖**:
  - PyQt6 (GUI框架)
  - beautifulsoup4 (HTML解析)
  - lxml (XML解析)
  - pywinauto (Windows自动化)
  - colorama (控制台颜色)

## 🎮 使用方法

### 快速启动
1. 双击 `启动程序.bat`
2. 程序会自动检查环境并安装依赖
3. 启动成功后配置邮箱信息
4. 开始使用各项功能

### 手动启动
```cmd
# 安装依赖
pip install -r requirements.txt

# 运行程序
python 主程序.py
```

## 🔍 功能模块

### 1. 一键操作工具
- **一键登录**: 自动执行完整的登录流程
- **一键重置环境**: 重置Cursor的机器ID和相关配置

### 2. 邮箱管理中心
- **凭据设置**: 配置邮箱前缀和密码
- **随机邮箱**: 自动生成随机邮箱地址
- **邮箱监控器**: 独立的邮箱监控窗口

### 3. 浏览器设置
- **路径配置**: 设置Chrome浏览器可执行文件路径
- **自动检测**: 自动检测系统中的Chrome安装

### 4. 操作日志
- **实时显示**: 所有操作的实时状态反馈
- **自动滚动**: 自动滚动到最新消息
- **验证码处理**: 自动复制验证码到剪贴板

## 🎨 界面特色

- **现代化配色**: 蓝色主题，清新简洁
- **图标丰富**: 使用表情符号增强视觉效果
- **状态反馈**: 实时的状态指示和进度显示
- **用户友好**: 直观的操作流程和错误提示

## 🔧 开发说明

### 代码结构
- **主程序.py**: 包含所有GUI组件和主窗口类
- **配置管理.py**: 处理配置文件和注册表操作
- **邮箱管理.py**: 邮箱相关功能和监控线程
- **应用管理.py**: Cursor应用的管理和重置功能

### 设计模式
- **信号槽机制**: 使用PyQt6的信号槽进行组件通信
- **多线程**: 后台任务使用独立线程避免界面冻结
- **模块化**: 功能模块化设计，便于维护和扩展

## 📝 更新日志

### v2.0.0 (PyQt6版本)
- 🎨 全新PyQt6界面设计
- 🚀 Windows专用优化
- 📱 响应式布局
- 🔧 改进的配置管理
- 🎯 更好的用户体验
- 📊 实时状态反馈
- 🔄 自动复制验证码

## 🤝 贡献

本项目是原tkinter版本的现代化重写，保持了所有原有功能的同时，提供了更好的用户体验。

---

**享受更美观的续杯体验！** 🎉

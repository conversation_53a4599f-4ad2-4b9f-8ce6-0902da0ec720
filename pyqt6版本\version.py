"""
续杯工具 PyQt6版本 - 版本信息
"""

__version__ = "2.0.0"
__author__ = "续杯工具开发团队"
__description__ = "Cursor管理助手 - PyQt6现代化版本"
__license__ = "MIT"

# 版本历史
VERSION_HISTORY = {
    "2.0.0": {
        "date": "2024-12-29",
        "changes": [
            "全新PyQt6界面设计",
            "现代化的UI主题和布局",
            "改进的多线程处理",
            "更好的错误处理机制",
            "响应式界面布局",
            "自动复制验证码功能",
            "完善的配置管理系统"
        ]
    }
}

def get_version_info():
    """获取版本信息"""
    return {
        "version": __version__,
        "author": __author__,
        "description": __description__,
        "license": __license__
    }

def print_version():
    """打印版本信息"""
    print(f"续杯工具 PyQt6版本 v{__version__}")
    print(f"作者: {__author__}")
    print(f"描述: {__description__}")
    print(f"许可证: {__license__}")

if __name__ == "__main__":
    print_version()

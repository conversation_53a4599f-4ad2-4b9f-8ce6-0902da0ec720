import os
import sys
import json
import uuid
import sqlite3
import platform
import re
import traceback
import subprocess
import time

# 从主程序导入EMOJI常量
try:
    from 主程序 import EMOJI
except ImportError:
    # 如果主程序未导入，定义基本的EMOJI常量
    EMOJI = {
        "INFO": "ℹ️",
        "ERROR": "❌",
        "SUCCESS": "✅",
        "WARNING": "⚠️"
    }

# 不再使用日志文件记录

def get_cursor_paths():
    """获取Windows系统下Cursor相关文件的路径"""
    try:
        base_path = os.path.join(os.getenv("APPDATA"), "Cursor")

        # Define potential installation paths for Cursor on Windows
        app_path_system = os.path.join(os.getenv("ProgramFiles", "C:\\Program Files"), "cursor", "Cursor.exe")
        app_path_user = os.path.join(os.getenv("LOCALAPPDATA"), "Programs", "Cursor", "Cursor.exe")

        # Check for the executable, prioritizing the user-specific path
        if os.path.exists(app_path_user):
            app_path = app_path_user
        elif os.path.exists(app_path_system):
            app_path = app_path_system
        else:
            # Fallback to user path. The calling function will handle the error if it doesn't exist.
            app_path = app_path_user

    except Exception as e:
        # 不再记录到日志文件
        return None, None, None

    db_path = os.path.join(base_path, "User", "globalStorage", "state.vscdb")
    machine_id_path = os.path.join(base_path, "machineId")

    return db_path, machine_id_path, app_path

class MachineIDResetter:
    def __init__(self, status_callback):
        self.system = platform.system()
        self.db_path, self.machine_id_path, self.app_path = get_cursor_paths()
        self.status_callback = status_callback
        if not self.db_path:
             self.status_callback(f"错误: 无法获取Cursor路径，重置操作无法继续。")

    def generate_new_ids(self):
        """生成新的机器ID"""
        self.status_callback(f"信息: 正在生成新的机器ID...")
        new_ids = {
            "dev_device_id": str(uuid.uuid4()),
            "vscode_machine_id": f"vscode-machine-id-{uuid.uuid4()}",
            "vscode_telemetry_id": f"vscode-telemetry-machine-id-{uuid.uuid4()}",
            "telemetry_device_id": str(uuid.uuid4()).replace("-", ""), # Typically a hex string without dashes
        }
        self.status_callback(f"成功: 新的机器ID已生成。")
        return new_ids

    def update_sqlite_db(self, new_ids):
        """
        更新SQLite数据库中的ID。
        使用 "UPDATE or INSERT" (UPSERT) 逻辑确保键值被正确设置。
        """
        if not os.path.exists(self.db_path):
            self.status_callback(f"警告: 未找到SQLite数据库，跳过更新: {self.db_path}")
            return

        self.status_callback(f"信息: 正在更新SQLite数据库: {self.db_path}")
        
        # 定义要更新的键值对
        items_to_update = {
            "telemetry.devDeviceId": new_ids["dev_device_id"],
            "telemetry.machineId": new_ids["vscode_machine_id"],
            "telemetry.sqmId": new_ids["vscode_telemetry_id"],
        }

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            for key, value in items_to_update.items():
                # Use INSERT OR REPLACE to handle both insert and update cases
                cursor.execute("""
                    INSERT OR REPLACE INTO ItemTable (key, value) 
                    VALUES (?, ?)
                """, (key, value))
                self.status_callback(f"已更新数据库键: {key}")

            conn.commit()
            conn.close()
            self.status_callback(f"成功: SQLite数据库更新完成。")

        except sqlite3.Error as e:
            self.status_callback(f"错误: 更新SQLite数据库时出错: {e}")
        except Exception as e:
            self.status_callback(f"错误: 更新SQLite数据库时发生未知错误: {e}")

    def update_machine_id_file(self, new_ids):
        """更新机器ID文件"""
        if not os.path.exists(self.machine_id_path):
            self.status_callback(f"警告: 未找到机器ID文件，跳过更新: {self.machine_id_path}")
            return

        self.status_callback(f"信息: 正在更新机器ID文件: {self.machine_id_path}")
        
        try:
            with open(self.machine_id_path, 'w', encoding='utf-8') as f:
                f.write(new_ids["vscode_machine_id"])
            self.status_callback(f"成功: 机器ID文件更新完成。")
        except Exception as e:
            self.status_callback(f"错误: 更新机器ID文件时出错: {e}")

    def kill_cursor_processes(self):
        """终止所有Cursor进程 (Windows)"""
        self.status_callback(f"信息: 正在终止Cursor进程...")

        try:
            # Windows: 使用taskkill命令
            subprocess.run(["taskkill", "/f", "/im", "Cursor.exe"],
                         capture_output=True, text=True, check=False)
            subprocess.run(["taskkill", "/f", "/im", "cursor.exe"],
                         capture_output=True, text=True, check=False)

            self.status_callback(f"成功: Cursor进程已终止。")

        except Exception as e:
            self.status_callback(f"警告: 终止Cursor进程时出错: {e}")

    def reset_machine_id(self):
        """执行完整的机器ID重置流程"""
        self.status_callback(f"开始重置Cursor机器ID...")
        
        # 1. 终止Cursor进程
        self.kill_cursor_processes()
        
        # 2. 生成新的ID
        new_ids = self.generate_new_ids()
        
        # 3. 更新SQLite数据库
        self.update_sqlite_db(new_ids)
        
        # 4. 更新机器ID文件
        self.update_machine_id_file(new_ids)
        
        self.status_callback(f"机器ID重置完成！")
        return True

def run_full_reset_flow(status_callback):
    """运行完整的重置流程"""
    try:
        status_callback("开始执行完整重置流程...")
        
        # 创建机器ID重置器
        resetter = MachineIDResetter(status_callback)
        
        # 执行重置
        success = resetter.reset_machine_id()
        
        if success:
            status_callback(f"{EMOJI['SUCCESS']} 重置流程完成！请重新启动Cursor。")
        else:
            status_callback(f"{EMOJI['ERROR']} 重置流程失败。")
            
    except Exception as e:
        status_callback(f"{EMOJI['ERROR']} 重置过程中发生错误: {e}")
        status_callback(traceback.format_exc())

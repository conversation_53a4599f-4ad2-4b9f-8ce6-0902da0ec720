import os
import sys
import json
import uuid
import sqlite3
import platform
import re
import traceback
import subprocess
import time

# 从主程序导入EMOJI常量
try:
    from 主程序 import EMOJI
except ImportError:
    # 如果主程序未导入，定义基本的EMOJI常量
    EMOJI = {
        "INFO": "ℹ️",
        "ERROR": "❌",
        "SUCCESS": "✅",
        "WARNING": "⚠️",
        "RESET": "🔄"
    }

# 不再使用日志文件记录

def get_cursor_paths():
    """获取Cursor相关文件的路径，支持多平台"""
    system = platform.system()

    try:
        if system == "Windows":
            base_path = os.path.join(os.getenv("APPDATA"), "Cursor")

            # Define potential installation paths for Cursor on Windows
            app_path_system = os.path.join(os.getenv("ProgramFiles", "C:\\Program Files"), "cursor", "Cursor.exe")
            app_path_user = os.path.join(os.getenv("LOCALAPPDATA"), "Programs", "Cursor", "Cursor.exe")

            # Check for the executable, prioritizing the user-specific path
            if os.path.exists(app_path_user):
                app_path = app_path_user
            elif os.path.exists(app_path_system):
                app_path = app_path_system
            else:
                app_path = app_path_user

        elif system == "Darwin":  # macOS
            base_path = os.path.expanduser("~/Library/Application Support/Cursor")
            app_path = "/Applications/Cursor.app"

        elif system == "Linux":
            base_path = os.path.expanduser("~/.config/Cursor")
            app_path = "/usr/bin/cursor"  # Common installation path

        else:
            return None, None, None

    except Exception as e:
        return None, None, None

    db_path = os.path.join(base_path, "User", "globalStorage", "state.vscdb")

    # Machine ID filename varies by system
    machine_id_filename = "machineId" if system != "Linux" else "machineid"
    machine_id_path = os.path.join(base_path, machine_id_filename)

    return db_path, machine_id_path, app_path

class MachineIDResetter:
    def __init__(self, status_callback):
        self.system = platform.system()
        self.db_path, self.machine_id_path, self.app_path = get_cursor_paths()
        self.status_callback = status_callback
        if not self.db_path:
             self.status_callback(f"错误: 无法获取Cursor路径，重置操作无法继续。")

    def generate_new_ids(self):
        """生成新的机器ID"""
        self.status_callback(f"信息: 正在生成新的机器ID...")
        new_ids = {
            "dev_device_id": str(uuid.uuid4()),
            "vscode_machine_id": f"vscode-machine-id-{uuid.uuid4()}",
            "vscode_telemetry_id": f"vscode-telemetry-machine-id-{uuid.uuid4()}",
            "telemetry_device_id": str(uuid.uuid4()).replace("-", ""), # Typically a hex string without dashes
        }
        self.status_callback(f"成功: 新的机器ID已生成。")
        return new_ids

    def update_sqlite_db(self, new_ids):
        """
        更新SQLite数据库中的ID。
        使用 "UPDATE or INSERT" (UPSERT) 逻辑确保键值被正确设置。
        """
        if not os.path.exists(self.db_path):
            self.status_callback(f"警告: 未找到SQLite数据库，跳过更新: {self.db_path}")
            return

        self.status_callback(f"信息: 正在更新SQLite数据库: {self.db_path}")
        
        # 定义要更新的键值对
        items_to_update = {
            "telemetry.machineId": json.dumps({"id": new_ids["vscode_telemetry_id"]}),
            "storage.machineId": new_ids["vscode_machine_id"],
            "telemetry.deviceId": new_ids["telemetry_device_id"],
        }

        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                for key, value in items_to_update.items():
                    # 尝试更新
                    cursor.execute("UPDATE ItemTable SET value = ? WHERE key = ?", (value, key))
                    # 如果没有行被更新 (即键不存在), 则插入
                    if cursor.rowcount == 0:
                        self.status_callback(f"信息: 键 '{key}' 不存在，正在插入...")
                        cursor.execute("INSERT INTO ItemTable (key, value) VALUES (?, ?)", (key, value))

            self.status_callback(f"成功: SQLite数据库更新成功。")

        except sqlite3.Error as e:
            self.status_callback(f"错误: 更新SQLite数据库时出错: {e}")
        except Exception as e:
            self.status_callback(f"错误: 更新SQLite数据库时发生未知错误: {e}")

    def update_machine_id_file(self, machine_id: str):
        """更新machineId文件"""
        if not self.machine_id_path: return
        self.status_callback(f"信息: 正在更新machineId文件: {self.machine_id_path}")
        try:
            os.makedirs(os.path.dirname(self.machine_id_path), exist_ok=True)
            with open(self.machine_id_path, "w", encoding="utf-8") as f:
                f.write(machine_id)
            self.status_callback(f"成功: machineId文件更新成功。")
        except IOError as e:
            self.status_callback(f"错误: 更新machineId文件失败: {e}")

    def update_system_specific_ids(self):
        """更新特定于操作系统的ID"""
        try:
            if self.system == "Windows":
                self._update_windows_machine_guid()
            elif self.system == "Darwin":
                self._update_macos_platform_uuid()
        except Exception as e:
            self.status_callback(f"错误: 更新系统特定ID时出错: {e}")
            self.status_callback(traceback.format_exc())

    def _update_windows_machine_guid(self):
        """更新Windows注册表中的MachineGuid"""
        self.status_callback(f"信息: 正在更新Windows注册表中的MachineGuid...")
        try:
            import winreg
            key_path = r"SOFTWARE\\Microsoft\\Cryptography"
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, key_path, 0, winreg.KEY_SET_VALUE) as key:
                winreg.SetValueEx(key, "MachineGuid", 0, winreg.REG_SZ, str(uuid.uuid4()))
            self.status_callback(f"成功: Windows MachineGuid更新成功。")
        except (ImportError, OSError) as e:
            self.status_callback(f"错误: 更新Windows MachineGuid失败: {e}")
            self.status_callback(f"警告: 这通常需要管理员权限。")

    def _update_macos_platform_uuid(self):
        """处理macOS的平台UUID"""
        self.status_callback(f"信息: 正在检查macOS平台UUID...")
        try:
            result = subprocess.run(
                ["ioreg", "-d2", "-c", "IOPlatformExpertDevice"],
                capture_output=True, text=True, check=True
            )
            current_uuid = re.search(r'"IOPlatformUUID"\s*=\s*"([^"]+)"', result.stdout)
            if current_uuid:
                self.status_callback(f"信息: 当前macOS平台UUID: {current_uuid.group(1)}")
            self.status_callback(f"警告: 注意：直接更改macOS平台UUID很复杂且有风险。脚本不会执行此操作。")
        except (subprocess.CalledProcessError, FileNotFoundError):
            self.status_callback(f"警告: 无法检查macOS平台UUID。可能是ioreg命令不可用。")

    def run(self):
        """执行重置流程"""
        if not self.db_path: return

        self.status_callback(f"开始重置Cursor机器ID")

        new_ids = self.generate_new_ids()
        self.update_sqlite_db(new_ids)
        self.update_system_specific_ids()
        self.update_machine_id_file(new_ids["dev_device_id"])

        self.status_callback(f"\n成功: 机器ID重置完成！")
        self.status_callback(f"警告: 请重启Cursor使更改完全生效。")

    def kill_cursor_processes(self):
        """终止所有Cursor进程 (Windows)"""
        self.status_callback(f"信息: 正在检查并终止Cursor进程...")

        try:
            # 首先检查是否有Cursor进程在运行
            check_result = subprocess.run(["tasklist", "/fi", "imagename eq Cursor.exe"],
                                        capture_output=True, text=True, check=False)

            cursor_running = "Cursor.exe" in check_result.stdout

            if cursor_running:
                self.status_callback(f"检测到正在运行的Cursor进程，正在终止...")

                # 尝试优雅关闭
                self.status_callback(f"尝试优雅关闭Cursor...")
                subprocess.run(["taskkill", "/im", "Cursor.exe"],
                             capture_output=True, text=True, check=False)

                # 等待一下让进程有时间关闭
                time.sleep(2)

                # 检查是否还在运行，如果是则强制终止
                check_result2 = subprocess.run(["tasklist", "/fi", "imagename eq Cursor.exe"],
                                             capture_output=True, text=True, check=False)

                if "Cursor.exe" in check_result2.stdout:
                    self.status_callback(f"优雅关闭失败，正在强制终止...")
                    subprocess.run(["taskkill", "/f", "/im", "Cursor.exe"],
                                 capture_output=True, text=True, check=False)
                    # 移除对cursor.exe的终止，只针对Cursor.exe

                self.status_callback(f"成功: Cursor进程已终止。")
            else:
                self.status_callback(f"信息: 未检测到正在运行的Cursor进程。")

        except Exception as e:
            self.status_callback(f"警告: 终止Cursor进程时出错: {e}")
            # 即使出错也尝试强制终止Cursor
            try:
                subprocess.run(["taskkill", "/f", "/im", "Cursor.exe"],
                             capture_output=True, text=True, check=False)
                # 移除对cursor.exe的终止，只针对Cursor.exe
            except:
                pass

    def reset_machine_id(self):
        """执行完整的机器ID重置流程"""
        self.status_callback(f"{EMOJI['RESET']} 开始重置Cursor机器ID...")

        # 1. 终止Cursor进程
        self.status_callback(f"步骤 1/4: 检查并关闭Cursor进程")
        self.kill_cursor_processes()

        # 2. 生成新的ID
        self.status_callback(f"步骤 2/4: 生成新的机器ID")
        new_ids = self.generate_new_ids()

        # 3. 更新SQLite数据库
        self.status_callback(f"步骤 3/4: 更新数据库配置")
        self.update_sqlite_db(new_ids)

        # 4. 更新机器ID文件
        self.status_callback(f"步骤 4/4: 更新机器ID文件")
        self.update_machine_id_file(new_ids)

        self.status_callback(f"{EMOJI['SUCCESS']} 机器ID重置完成！")
        self.status_callback(f"提示: 现在可以重新启动Cursor使用新的机器ID。")
        return True

def launch_cursor(status_callback):
    """以普通用户权限启动Cursor应用程序"""
    status_callback("正在尝试启动Cursor")
    _, _, app_path = get_cursor_paths()

    if not app_path or not os.path.exists(app_path):
        status_callback(f"错误: 未找到Cursor应用程序路径，无法启动。")
        status_callback(f"预期路径: {app_path}")
        return False

    try:
        if platform.system() == "Darwin": # macOS
            subprocess.Popen(['open', app_path])
        elif platform.system() == "Windows":
            # 在Windows上以普通用户权限启动
            try:
                # 使用explorer.exe来启动，这样可以避免继承管理员权限
                subprocess.Popen(['explorer.exe', app_path])
                status_callback(f"成功: 已以普通用户权限启动Cursor")

            except Exception as e:
                # 如果特殊方法失败，使用普通方法
                status_callback(f"警告: 无法以普通权限启动，使用默认方式: {e}")
                subprocess.Popen([app_path])
                status_callback(f"成功: 已启动Cursor（可能继承管理员权限）")
        else:
            subprocess.Popen([app_path])
            status_callback(f"成功: 已发送启动指令到: {app_path}")
        return True
    except Exception as e:
        status_callback(f"错误: 启动Cursor时发生错误: {e}")
        status_callback(traceback.format_exc())
        return False

def terminate_cursor_processes(status_callback):
    """
    Terminates all running Cursor processes using OS-specific commands.
    Returns a tuple: (number_of_processes_killed, all_commands_found_or_not_needed)
    """
    system = platform.system()
    status_callback(f"正在尝试关闭所有Cursor进程")

    processes_killed_total = 0
    all_found = True # Assume all commands will be found

    if system == "Windows":
        process_names = ["Cursor.exe"]  # 只终止Cursor，不影响VS Code
    else: # Darwin (macOS) & Linux
        process_names = ["cursor"]  # 只终止Cursor，不影响Code - OSS

    try:
        for name in process_names:
            if system == "Windows":
                # For Windows, use taskkill. Encoding is important for non-English systems.
                try:
                    command = ['taskkill', '/F', '/IM', name]
                    result = subprocess.run(command, capture_output=True, text=True, check=False, encoding='gbk', errors='ignore')

                    if result.returncode == 0:
                        status_callback(f"成功: 成功终止进程: {name}")
                        processes_killed_total += 1
                    elif result.returncode == 128:
                        # Return code 128 means process not found, which is expected if it's not running.
                        pass
                    else:
                        error_message = result.stderr or result.stdout
                        status_callback(f"警告: 终止 '{name}' 时: {error_message.strip()}")
                except FileNotFoundError:
                    status_callback(f"错误: 命令 'taskkill' 未找到。")
                    all_found = False
                    break # Stop if the command doesn't exist

            elif system in ["Darwin", "Linux"]:
                # For macOS and Linux, use pkill.
                try:
                    command = ['pkill', '-f', name]
                    result = subprocess.run(command, capture_output=True, text=True, check=False)
                    if result.returncode == 0:
                        status_callback(f"成功: 成功终止与 '{name}' 相关的进程。")
                        processes_killed_total += 1
                    elif result.returncode == 1:
                        # Return code 1 means no processes matched.
                        pass
                    else:
                        error_message = result.stderr or result.stdout
                        status_callback(f"警告: 终止 '{name}' 时: {error_message.strip()}")
                except FileNotFoundError:
                    status_callback(f"错误: 命令 'pkill' 未找到。")
                    all_found = False
                    break # Stop if the command doesn't exist

    except Exception as e:
        status_callback(f"错误: 关闭Cursor进程时发生未知错误: {e}")
        status_callback(traceback.format_exc())

    # Final summary message
    if processes_killed_total > 0:
        status_callback(f"\n成功: 操作完成，相关进程已关闭。")
    else:
        status_callback(f"\n信息: 未找到正在运行的Cursor相关进程。")

    return processes_killed_total, all_found

def run_full_reset_flow(status_callback):
    """
    执行完整的环境重置流程：
    1. 终止 Cursor 进程。
    2. 重置机器 ID。
    3. 重新启动 Cursor。
    """
    try:
        processes_killed, all_found = terminate_cursor_processes(status_callback)
        if not all_found:
             status_callback(f"错误: 无法确认Cursor进程已关闭，中止操作。")
             return

        status_callback("\n准备重置机器ID")
        time.sleep(1)

        try:
            if sys.platform == "win32":
                import ctypes
                if not ctypes.windll.shell32.IsUserAnAdmin():
                    status_callback(f"错误: 权限不足！重置ID需要管理员权限。")
                    return
        except (ImportError, AttributeError):
             status_callback(f"警告: 无法检查管理员权限，请确保以管理员身份运行。")

        resetter = MachineIDResetter(status_callback)
        resetter.run()
        status_callback(f"\n成功: ID重置完成，准备重启应用...")
        time.sleep(1)

        launch_cursor(status_callback)

        status_callback(f"\n\n成功: 环境重置流程执行完毕！")

    except Exception as e:
        status_callback(f"错误: 环境重置过程中发生未知错误: {e}")
        status_callback(traceback.format_exc())

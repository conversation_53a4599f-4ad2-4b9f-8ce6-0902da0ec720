import poplib
import email
import re
import time
import sys
import os
import queue
import threading
import multiprocessing
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QTextEdit, QLineEdit, QGroupBox,
                            QMessageBox, QApplication)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont
from bs4 import BeautifulSoup
import random
import string

from 配置管理 import get_config, save_config

# EMOJI常量定义
EMOJI = {
    "INFO": "ℹ️",
    "ERROR": "❌",
    "SUCCESS": "✅",
    "WARNING": "⚠️",
    "LOGIN": "🔑",
    "EMAIL": "📧",
    "USER": "👤",
    "COPY": "📋",
    "RESET": "🔄",
    "START": "▶️",
    "STOP": "⏹️"
}

# 使用绝对路径确保文件位置统一
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))

# --- 全局配置 ---
POP3_SERVER = 'pop.2925.com'
POP3_PORT = 110
REFRESH_INTERVAL_SECONDS = 1
MAX_WAIT_SECONDS = 90

class EmailManager:
    """统一的邮箱管理类，整合所有邮箱相关功能"""
    
    def __init__(self):
        self.config = get_config()
        self.email_prefix = ""
        self.email_password = ""
        self.load_credentials()
    
    def load_credentials(self):
        """从配置文件加载邮箱凭据"""
        if self.config:
            self.email_prefix = self.config.get('Email', 'prefix', fallback='').strip()
            self.email_password = self.config.get('Email', 'password', fallback='').strip()
    
    def save_credentials(self, prefix, password):
        """保存邮箱凭据到配置文件和注册表"""
        if not self.config:
            return False

        # 保存到配置文件
        self.config.set('Email', 'prefix', prefix)
        self.config.set('Email', 'password', password)

        config_saved = save_config(self.config)

        # 尝试保存到注册表（如果是Windows系统）
        registry_saved = True  # 默认认为成功，非Windows系统不影响
        try:
            from 配置管理 import set_registry_email_config
            registry_saved = set_registry_email_config(prefix, password)
        except Exception as e:
            # 注册表保存失败不影响配置文件保存
            print(f"注册表保存失败: {e}")

        if config_saved:
            self.email_prefix = prefix
            self.email_password = password
            return True
        return False
    
    def generate_random_email(self):
        """生成随机邮箱地址"""
        if not self.email_prefix:
            return None
        
        random_part = ''.join(random.choices(string.ascii_lowercase, k=7))
        return f"{self.email_prefix}{random_part}@2925.com"
    
    def get_monitoring_email(self):
        """获取用于监控的固定邮箱地址"""
        if not self.email_prefix:
            return None
        return f"{self.email_prefix}@2925.com"

    def launch_email_client_process(self, email, password, status_callback, show_status=True, code_container=None):
        """使用 multiprocessing 在后台启动邮件客户端脚本"""
        try:
            if show_status:
                status_callback("正在验证邮箱凭据...")

            # 首先验证邮箱凭据
            if not self.verify_email_credentials(email, password, status_callback):
                if show_status:
                    status_callback("❌ 验证失败，停止启动邮箱客户端")
                return False

            if show_status:
                status_callback("✅ 验证成功，开始启动邮箱客户端进程")

            output_queue = multiprocessing.Queue()

            # 创建并启动子进程
            process = multiprocessing.Process(
                target=self.monitor_email,
                args=(email, password, output_queue)
            )
            process.daemon = True
            process.start()

            def log_output():
                """从队列中读取并处理子进程的输出"""
                while process.is_alive() or not output_queue.empty():
                    try:
                        line = output_queue.get(timeout=0.1)
                        stripped_line = line.strip()
                        # IPC logic to capture the code
                        if stripped_line.startswith("VERIFICATION_CODE:"):
                            try:
                                code = stripped_line.split(":", 1)[1]
                                if code_container is not None:
                                    code_container.append(code)
                            except IndexError:
                                pass

                        # Logging logic
                        status_callback(f"[邮箱客户端]: {stripped_line}")
                    except queue.Empty:
                        continue

                # 确保进程结束后清空队列
                while not output_queue.empty():
                    try:
                        line = output_queue.get_nowait()
                        status_callback(f"[邮箱客户端]: {line.strip()}")
                    except queue.Empty:
                        break

            threading.Thread(target=log_output, daemon=True).start()

            if show_status:
                status_callback(f"✅ 邮箱验证成功，监控已启动: {email}")
            return True

        except Exception as e:
            if show_status:
                status_callback(f"❌ 启动邮箱客户端时发生错误: {e}")
            return False

    def verify_email_credentials(self, email, password, status_callback):
        """验证邮箱凭据是否正确"""
        try:
            status_callback("正在连接邮箱服务器...")
            server = poplib.POP3(POP3_SERVER, port=POP3_PORT, timeout=20)

            status_callback("正在验证用户名...")
            server.user(email)

            status_callback("正在验证密码...")
            server.pass_(password)

            status_callback("✅ 邮箱凭据验证成功")
            server.quit()
            return True

        except poplib.error_proto as e:
            status_callback(f"❌ 邮箱凭据验证失败: 登录失败，请检查凭据 ({e})")
            return False

        except Exception as e:
            status_callback(f"❌ 连接邮箱服务器失败: {e}")
            return False

    def wait_for_verification_code(self, status_callback, code_container):
        """轮询一个共享列表以获取验证码"""
        status_callback("正在等待接收验证码 (最长90秒)...")
        for i in range(MAX_WAIT_SECONDS):
            if code_container:
                code = code_container.pop(0)
                status_callback(f"COPY_AND_SHOW:{code}")
                return code

            # 每 15 秒更新一次状态
            if (i > 0 and (i + 1) % 15 == 0):
                status_callback(f"已等待 {i+1} 秒，接收中...")

            time.sleep(1)

        status_callback("❌ 等待验证码超时")
        return None

    def monitor_email(self, email_account, email_password, output_queue):
        """邮箱监控进程的主函数"""
        try:
            output_queue.put(f"正在监控邮箱: {email_account}")

            # 首次连接并建立基线
            try:
                server = poplib.POP3(POP3_SERVER, port=POP3_PORT, timeout=20)
                server.user(email_account)
                server.pass_(email_password)
                output_queue.put("验证成功。")

                resp, uid_lines, octets = server.uidl()
                seen_uids = {line.split()[1] for line in uid_lines}
                output_queue.put(f"基线已建立，当前有 {len(seen_uids)} 封邮件。开始监控新邮件...")
                server.quit()
            except poplib.error_proto as e:
                output_queue.put(f"错误：登录失败。请检查凭据。 ({e})")
                return
            except Exception as e:
                output_queue.put(f"连接或建立基线时发生未知错误: {e}")
                return

            # 进入监控循环
            loop_counter = 0
            while True:
                try:
                    server = poplib.POP3(POP3_SERVER, port=POP3_PORT, timeout=20)
                    server.user(email_account)
                    server.pass_(email_password)

                    resp, uid_lines, octets = server.uidl()

                    # 创建当前邮件UID到消息号的映射
                    current_uid_map = {
                        parts[1]: parts[0]
                        for line in uid_lines
                        if len(parts := line.split()) == 2
                    }

                    # 检查新邮件
                    new_uids = set(current_uid_map.keys()) - seen_uids

                    if new_uids:
                        output_queue.put(f"发现 {len(new_uids)} 封新邮件，正在检查验证码...")

                        for uid in new_uids:
                            msg_num = current_uid_map[uid]
                            try:
                                resp, lines, octets = server.retr(int(msg_num))
                                raw_email = b'\n'.join(lines)
                                msg = email.message_from_bytes(raw_email)

                                subject = msg.get('Subject', '无主题')
                                sender = msg.get('From', '未知发件人')

                                body_text = self.get_clean_body_from_msg(msg)
                                code = self.find_code_in_text(body_text)

                                if code:
                                    output_queue.put(f"VERIFICATION_CODE:{code}")
                                    seen_uids.add(uid)
                                    server.quit()
                                    return
                                else:
                                    output_queue.put(f"新邮件未包含验证码 - 发件人: {sender}, 主题: {subject}")

                            except Exception as e:
                                output_queue.put(f"处理邮件时出错: {e}")

                        seen_uids.update(new_uids)

                    server.quit()

                    loop_counter += 1
                    if loop_counter % 10 == 0:
                        output_queue.put(f"监控中... (已检查 {loop_counter} 次)")

                    time.sleep(REFRESH_INTERVAL_SECONDS)

                except Exception as e:
                    output_queue.put(f"监控过程中出错: {e}")
                    time.sleep(5)  # 出错时等待更长时间再重试

        except Exception as e:
            output_queue.put(f"监控进程发生致命错误: {e}")
    
    @staticmethod
    def decode_payload(payload, charset):
        """安全地用给定字符集解码负载"""
        try:
            return payload.decode(charset)
        except (UnicodeDecodeError, LookupError):
            return payload.decode('gbk', errors='ignore')
    
    @staticmethod
    def get_clean_body_from_msg(msg):
        """解析邮件消息并返回纯净的文本正文"""
        body_content, html_content = "", ""
        if msg.is_multipart():
            for part in msg.walk():
                if part.get('Content-Disposition', '').startswith('attachment'):
                    continue
                payload = part.get_payload(decode=True)
                if not payload:
                    continue
                charset = part.get_content_charset() or 'utf-8'
                content_type = part.get_content_type()
                if content_type == 'text/plain':
                    body_content = EmailManager.decode_payload(payload, charset)
                elif content_type == 'text/html':
                    html_content = EmailManager.decode_payload(payload, charset)
        else:
            if not msg.get('Content-Disposition', '').startswith('attachment'):
                payload = msg.get_payload(decode=True)
                charset = msg.get_content_charset() or 'utf-8'
                content_type = msg.get_content_type()
                if content_type == 'text/plain':
                    body_content = EmailManager.decode_payload(payload, charset)
                elif content_type == 'text/html':
                    html_content = EmailManager.decode_payload(payload, charset)
        
        if not body_content.strip() and html_content:
            soup = BeautifulSoup(html_content, 'lxml')
            return soup.get_text(separator='\n', strip=True)
        return body_content
    
    @staticmethod
    def find_code_in_text(body_text):
        """使用正则表达式在字符串中查找6位验证码"""
        patterns = [r'\b\d{6}\b', r'\b\d{3}\s\d{3}\b', r'\b(?:\d\s){5}\d\b']
        for pattern in patterns:
            match = re.search(pattern, body_text)
            if match:
                return match.group(0).replace(" ", "")
        return None
    
    def establish_baseline(self, server, output_queue):
        """获取当前所有邮件的UIDL，建立基线"""
        try:
            resp, uid_lines, octets = server.uidl()
            seen_uids = {line.split()[1] for line in uid_lines}
            output_queue.put(f"基线已建立，当前有 {len(seen_uids)} 封邮件。开始监控新邮件...")
            return seen_uids
        except Exception as e:
            output_queue.put(f"建立基线时出错: {e}")
            return None

# 创建全局邮箱管理器实例
email_manager = EmailManager()

def get_current_chrome_url():
    """获取当前Chrome浏览器的URL"""
    try:
        import pywinauto
        from pywinauto.application import Application

        app = Application(backend="uia").connect(title_re=".*- Google Chrome", class_name="Chrome_WidgetWin_1", timeout=10)
        dlg = app.top_window()
        wrapper = dlg.child_window(auto_id="view_1000", control_type="ToolBar")
        address_bar = wrapper.child_window(control_type="Edit")

        if address_bar.exists():
            url = address_bar.get_value()
            return url, dlg # Return both URL and the window handle
        else:
            return None, None

    except pywinauto.findwindows.ElementNotFoundError:
        print("Error: Google Chrome window not found.")
        return None, None
    except Exception as e:
        print(f"An unknown error occurred: {e}")
        return None, None

# 邮箱监控线程类
class EmailMonitorThread(QThread):
    """邮箱监控线程"""
    status_update = pyqtSignal(str)
    code_found = pyqtSignal(str)
    
    def __init__(self, email_account, email_password):
        super().__init__()
        self.email_account = email_account
        self.email_password = email_password
        self.should_stop = False
        self.manager = EmailManager()
    
    def stop(self):
        """停止监控"""
        self.should_stop = True
    
    def run(self):
        """运行邮箱监控"""
        self.status_update.emit(f"正在监控邮箱: {self.email_account}")
        
        # 首次连接并建立基线
        try:
            server = poplib.POP3(POP3_SERVER, port=POP3_PORT, timeout=20)
            server.user(self.email_account)
            server.pass_(self.email_password)
            self.status_update.emit("验证成功。")
            
            resp, uid_lines, octets = server.uidl()
            seen_uids = {line.split()[1] for line in uid_lines}
            self.status_update.emit(f"基线已建立，当前有 {len(seen_uids)} 封邮件。开始监控新邮件...")
            server.quit()
        except poplib.error_proto as e:
            self.status_update.emit(f"错误：登录失败。请检查凭据。 ({e})")
            return
        except Exception as e:
            self.status_update.emit(f"连接或建立基线时发生未知错误: {e}")
            return

        # 进入监控循环
        loop_counter = 0
        while not self.should_stop:
            try:
                server = poplib.POP3(POP3_SERVER, port=POP3_PORT, timeout=20)
                server.user(self.email_account)
                server.pass_(self.email_password)
                
                resp, uid_lines, octets = server.uidl()
                
                # 创建当前邮件UID到消息号的映射
                current_uid_map = {
                    parts[1]: parts[0]
                    for line in uid_lines
                    if len(parts := line.split()) == 2
                }
                
                # 检查新邮件
                new_uids = set(current_uid_map.keys()) - seen_uids
                
                if new_uids:
                    self.status_update.emit(f"发现 {len(new_uids)} 封新邮件，正在检查验证码...")
                    
                    for uid in new_uids:
                        msg_num = current_uid_map[uid]
                        try:
                            resp, lines, octets = server.retr(int(msg_num))
                            raw_email = b'\n'.join(lines)
                            msg = email.message_from_bytes(raw_email)
                            
                            subject = msg.get('Subject', '无主题')
                            sender = msg.get('From', '未知发件人')
                            
                            body_text = self.manager.get_clean_body_from_msg(msg)
                            code = self.manager.find_code_in_text(body_text)
                            
                            if code:
                                self.status_update.emit(f"COPY_AND_SHOW:{code}")
                                self.code_found.emit(code)
                                seen_uids.add(uid)
                                server.quit()
                                return
                            else:
                                self.status_update.emit(f"新邮件未包含验证码 - 发件人: {sender}, 主题: {subject}")
                        
                        except Exception as e:
                            self.status_update.emit(f"处理邮件时出错: {e}")
                    
                    seen_uids.update(new_uids)
                
                server.quit()
                
                loop_counter += 1
                if loop_counter % 10 == 0:
                    self.status_update.emit(f"监控中... (已检查 {loop_counter} 次)")
                
                time.sleep(REFRESH_INTERVAL_SECONDS)
                
            except Exception as e:
                self.status_update.emit(f"监控过程中出错: {e}")
                time.sleep(5)  # 出错时等待更长时间再重试

# 邮箱客户端窗口类
class EmailClientWindow(QDialog):
    """邮箱客户端窗口"""

    def __init__(self, parent=None, password=""):
        super().__init__(parent)
        self.password = password
        self.monitor_thread = None
        self.setup_ui()
        self.setup_connections()

    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle(f"{EMOJI['EMAIL']} 邮箱监控器")
        self.setMinimumSize(600, 500)
        self.resize(700, 600)

        # 设置样式
        self.setStyleSheet("""
            QDialog {
                background-color: #f8fafc;
                font-family: 'Microsoft YaHei UI', sans-serif;
            }
            QGroupBox {
                font-weight: bold;
                font-size: 11px;
                border: 2px solid #e5e7eb;
                border-radius: 8px;
                margin-top: 1ex;
                padding-top: 15px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #1f2937;
                background-color: white;
            }
            QPushButton {
                background-color: #2563eb;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 10px;
                min-height: 25px;
            }
            QPushButton:hover {
                background-color: #3b82f6;
            }
            QPushButton:pressed {
                background-color: #1d4ed8;
            }
            QPushButton:disabled {
                background-color: #e5e7eb;
                color: #9ca3af;
            }
            QPushButton.success {
                background-color: #10b981;
            }
            QPushButton.success:hover {
                background-color: #059669;
            }
            QPushButton.danger {
                background-color: #ef4444;
            }
            QPushButton.danger:hover {
                background-color: #dc2626;
            }
            QLineEdit {
                border: 2px solid #e5e7eb;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 10px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #2563eb;
            }
            QTextEdit {
                border: 2px solid #e5e7eb;
                border-radius: 6px;
                padding: 8px;
                font-family: 'Consolas', monospace;
                font-size: 16px;
                background-color: white;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 标题
        title_label = QLabel(f"{EMOJI['EMAIL']} 邮箱验证码监控器")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #1f2937; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # 邮箱设置组
        email_group = QGroupBox("📧 邮箱设置")
        email_layout = QVBoxLayout()

        # 邮箱地址输入
        email_input_layout = QHBoxLayout()
        email_input_layout.addWidget(QLabel("监控邮箱:"))
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("输入要监控的邮箱地址")
        self.email_input.setText(email_manager.get_monitoring_email() or "")
        email_input_layout.addWidget(self.email_input)
        email_layout.addLayout(email_input_layout)

        # 密码输入
        password_layout = QHBoxLayout()
        password_layout.addWidget(QLabel("邮箱密码:"))
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("输入邮箱密码")
        # 不设置密码隐藏模式，直接显示密码
        self.password_input.setText(self.password)
        password_layout.addWidget(self.password_input)
        email_layout.addLayout(password_layout)

        email_group.setLayout(email_layout)
        layout.addWidget(email_group)

        # 控制按钮
        button_layout = QHBoxLayout()

        self.start_button = QPushButton(f"{EMOJI['START']} 开始监控")
        self.start_button.setProperty("class", "success")
        button_layout.addWidget(self.start_button)

        self.stop_button = QPushButton(f"{EMOJI['STOP']} 停止监控")
        self.stop_button.setProperty("class", "danger")
        self.stop_button.setEnabled(False)
        button_layout.addWidget(self.stop_button)

        button_layout.addStretch()

        self.clear_button = QPushButton("🗑️ 清空日志")
        button_layout.addWidget(self.clear_button)

        layout.addLayout(button_layout)

        # 状态显示区域
        status_group = QGroupBox("📊 监控状态")
        status_layout = QVBoxLayout()

        self.status_text = QTextEdit()
        self.status_text.setReadOnly(True)
        self.status_text.setMinimumHeight(300)
        status_layout.addWidget(self.status_text)

        status_group.setLayout(status_layout)
        layout.addWidget(status_group)

        # 初始状态
        self.status_text.append("邮箱监控器已准备就绪。请输入邮箱信息并点击开始监控。")

    def setup_connections(self):
        """设置信号连接"""
        self.start_button.clicked.connect(self.start_monitoring)
        self.stop_button.clicked.connect(self.stop_monitoring)
        self.clear_button.clicked.connect(self.clear_log)

    def start_monitoring(self):
        """开始监控"""
        email = self.email_input.text().strip()
        password = self.password_input.text()

        if not email or not password:
            QMessageBox.warning(self, "输入错误", "请输入邮箱地址和密码")
            return

        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)

        self.monitor_thread = EmailMonitorThread(email, password)
        self.monitor_thread.status_update.connect(self.update_status)
        self.monitor_thread.code_found.connect(self.on_code_found)
        self.monitor_thread.finished.connect(self.on_monitoring_finished)
        self.monitor_thread.start()

        self.status_text.append(f"开始监控邮箱: {email}")

    def stop_monitoring(self):
        """停止监控"""
        if self.monitor_thread:
            self.monitor_thread.stop()
            self.monitor_thread.wait()

        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.status_text.append("监控已停止")

    def clear_log(self):
        """清空日志"""
        self.status_text.clear()
        self.status_text.append("日志已清空")

    def update_status(self, message):
        """更新状态显示"""
        # 检查是否是验证码复制消息
        if message.startswith("COPY_AND_SHOW:"):
            try:
                # 提取验证码
                code = message.split("COPY_AND_SHOW:", 1)[1].strip()

                # 自动复制到剪贴板
                clipboard = QApplication.clipboard()
                clipboard.setText(code)

                # 显示友好的消息
                display_message = f"✅ 验证码已获取并自动复制到剪贴板: {code}"
                self.status_text.append(display_message)
            except Exception as e:
                # 如果处理验证码失败，显示原始消息
                self.status_text.append(message)
        else:
            # 普通消息的处理
            self.status_text.append(message)

        # 自动滚动到底部
        cursor = self.status_text.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.status_text.setTextCursor(cursor)
        self.status_text.ensureCursorVisible()

    def on_code_found(self, code):
        """验证码找到时的处理"""
        QMessageBox.information(self, "验证码获取成功", f"验证码: {code}\n\n已自动复制到剪贴板")

    def on_monitoring_finished(self):
        """监控结束时的处理"""
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)

    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.monitor_thread and self.monitor_thread.isRunning():
            self.monitor_thread.stop()
            self.monitor_thread.wait()
        event.accept()

def show_email_client(parent=None, password=""):
    """显示邮箱客户端窗口"""
    dialog = EmailClientWindow(parent, password)
    dialog.show()
    return dialog

def launch_email_client_process(email, password, status_callback, show_status=True, code_container=None):
    """启动邮箱客户端进程（兼容原有接口）"""
    return email_manager.launch_email_client_process(email, password, status_callback, show_status, code_container)

def wait_for_verification_code(status_callback, code_container):
    """等待验证码（兼容原有接口）"""
    return email_manager.wait_for_verification_code(status_callback, code_container)

def run_auto_login_flow(monitoring_email, login_email, password, status_callback):
    """执行完整的自动登录流程"""
    chrome_window_to_close = None
    try:
        if not password:
            status_callback(f"错误: 邮箱密码未设置，请在凭据中设置并保存。")
            return

        if not monitoring_email or "@" not in monitoring_email:
            status_callback(f"错误: 用于监控的邮箱无效。")
            return

        status_callback("正在启动邮箱监控...")
        code_container = [] # 创建共享列表
        if not launch_email_client_process(monitoring_email, password, status_callback, show_status=False, code_container=code_container):
            status_callback(f"错误: 无法启动邮箱客户端，自动登录中止。")
            return
        status_callback(f"邮箱监控已在后台启动，监控账号: {monitoring_email}")

        status_callback("正在获取当前Chrome浏览器URL...")
        target_url, chrome_window_to_close = get_current_chrome_url()
        if not target_url:
            status_callback(f"错误: 获取URL失败，请确保Chrome在前台打开了一个页面。")
            return

        status_callback("正在打开新的浏览器窗口...")
        try:
            from DrissionPage import ChromiumOptions, ChromiumPage
        except ImportError:
            status_callback(f"错误: 未安装DrissionPage库，请运行: pip install DrissionPage")
            return

        co = ChromiumOptions()

        # 从配置中读取浏览器路径并设置
        from 配置管理 import get_config
        config = get_config()
        if config:
            chrome_path = config.get('Browser', 'chrome_path', fallback=None)
            if chrome_path and os.path.exists(chrome_path):
                co.set_paths(browser_path=chrome_path)
                status_callback(f"使用配置文件中的Chrome路径: {chrome_path}")
            else:
                status_callback("配置文件中未找到有效Chrome路径，使用默认路径。")

        co.set_argument('--incognito')
        page = ChromiumPage(addr_or_opts=co)

        if chrome_window_to_close:
            status_callback("正在关闭原始Chrome窗口...")
            chrome_window_to_close.close()
            status_callback("原始窗口已关闭。")

        if not target_url.startswith('http'):
            target_url = 'https://' + target_url
        status_callback(f"成功获取URL: {target_url}")

        status_callback(f"使用随机邮箱进行登录: {login_email}")

        automate_login(page, target_url, login_email, status_callback, code_container)

    except Exception as e:
        status_callback(f"错误: 一键登录过程中发生未知错误: {e}")
        import traceback
        status_callback(traceback.format_exc())

def automate_login(page, url, email, status_callback, code_container):
    """使用 DrissionPage 的核心自动化任务"""
    try:
        status_callback(f"正在加载URL: {url}")
        page.get(url)

        # 步骤 1: 填写邮箱并点击 "Continue"
        status_callback("步骤 1: 正在填写邮箱并点击 'Continue'...")
        email_input = page.ele('@@name=email', timeout=10)
        email_input.input(email)
        continue_button = page.ele('@@type=submit', timeout=10)
        continue_button.click()
        status_callback("已点击 'Continue'，等待跳转...")

        # 步骤 2: 在下一页点击 "Email sign-in code"
        status_callback("步骤 2: 等待密码页面加载，准备点击验证码登录...")
        sign_in_code_button = page.ele('text:Email sign-in code', timeout=15)
        sign_in_code_button.click()
        status_callback("已点击 'Email sign-in code' 按钮。")

        # 步骤 3: 等待验证码
        status_callback("步骤 3: 启动验证码监听程序...")
        code = wait_for_verification_code(status_callback, code_container)

        if not code:
            final_message = "\n错误: 等待验证码超时。请手动输入验证码完成登录。\n\n注意：操作完成后，请您手动关闭此浏览器窗口。"
            status_callback(final_message)
            return

        # 步骤 4: 自动填写验证码
        try:
            status_callback(f"步骤 4: 成功获取验证码，正在自动填写...")
            for i, digit in enumerate(code):
                input_box = page.ele(f"@data-index={i}")
                input_box.input(digit)
                time.sleep(random.uniform(0.1, 0.3))

            status_callback("✅ 验证码填写完成。")
            final_message = "\n✅ 成功: 验证码已自动填写。\n\n如果页面没有自动跳转，请手动完成最后步骤。完成后请手动关闭此浏览器窗口。"
            status_callback(final_message)

        except Exception as e:
            status_callback(f"❌ 错误: 自动填写验证码时出错: {e}")
            final_message = "自动化流程中止，请手动输入验证码。"
            status_callback(final_message)

        return

    except Exception as e:
        error_msg = f"错误: 自动化过程中发生错误: {e}"
        status_callback(error_msg)
        import traceback
        status_callback(traceback.format_exc())

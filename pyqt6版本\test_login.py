#!/usr/bin/env python3
"""
测试一键登录功能的完整性
"""

import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_login_functions():
    """测试登录相关函数的导入"""
    print("🔍 测试一键登录功能...")
    
    try:
        print("1. 测试邮箱管理模块...")
        from 邮箱管理 import (
            email_manager, 
            run_auto_login_flow, 
            get_current_chrome_url,
            launch_email_client_process,
            wait_for_verification_code,
            automate_login
        )
        print("   ✅ 邮箱管理模块导入成功")
    except ImportError as e:
        print(f"   ❌ 邮箱管理模块导入失败: {e}")
        return False
    
    try:
        print("2. 测试配置管理模块...")
        from 配置管理 import get_config, save_config
        config = get_config()
        if config:
            print("   ✅ 配置管理模块正常")
        else:
            print("   ⚠️ 配置文件加载失败")
    except Exception as e:
        print(f"   ❌ 配置管理模块测试失败: {e}")
        return False
    
    try:
        print("3. 测试应用管理模块...")
        from 应用管理 import run_full_reset_flow
        print("   ✅ 应用管理模块导入成功")
    except ImportError as e:
        print(f"   ❌ 应用管理模块导入失败: {e}")
        return False
    
    try:
        print("4. 测试主程序模块...")
        from 主程序 import MainWindow, create_gui
        print("   ✅ 主程序模块导入成功")
    except ImportError as e:
        print(f"   ❌ 主程序模块导入失败: {e}")
        return False
    
    print("\n🎉 所有登录功能模块测试通过！")
    return True

def test_dependencies():
    """测试依赖包"""
    print("\n🔍 测试依赖包...")
    
    dependencies = [
        ("PyQt6", "PyQt6.QtWidgets"),
        ("beautifulsoup4", "bs4"),
        ("lxml", "lxml"),
        ("pywinauto", "pywinauto"),
        ("DrissionPage", "DrissionPage")
    ]
    
    missing_deps = []
    
    for dep_name, import_name in dependencies:
        try:
            __import__(import_name)
            print(f"   ✅ {dep_name}")
        except ImportError:
            print(f"   ❌ {dep_name} - 未安装")
            missing_deps.append(dep_name)
    
    if missing_deps:
        print(f"\n⚠️ 缺少依赖包: {', '.join(missing_deps)}")
        print("请运行: pip install " + " ".join(missing_deps))
        return False
    
    print("\n✅ 所有依赖包都已安装")
    return True

if __name__ == "__main__":
    print("=" * 50)
    print("续杯工具 PyQt6版本 - 一键登录功能测试")
    print("=" * 50)
    
    # 测试依赖包
    deps_ok = test_dependencies()
    
    if deps_ok:
        # 测试登录功能
        login_ok = test_login_functions()
        
        if login_ok:
            print("\n✅ 一键登录功能完整，可以正常使用！")
            print("\n📋 使用说明:")
            print("1. 运行主程序: python 主程序.py")
            print("2. 设置邮箱凭据并保存")
            print("3. 在Chrome中打开目标网站")
            print("4. 点击'一键登录'按钮")
        else:
            print("\n❌ 一键登录功能存在问题，请检查错误信息")
    else:
        print("\n❌ 请先安装缺失的依赖包")
    
    print("\n" + "=" * 50)

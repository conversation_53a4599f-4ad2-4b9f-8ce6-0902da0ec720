# 续杯工具 - PyQt6版本

这是续杯工具的PyQt6重写版本，提供了更现代化和美观的用户界面。

## 功能特性

- 🎨 **现代化UI设计** - 使用PyQt6构建的美观界面
- 🔑 **一键登录** - 快速执行登录操作
- 🔄 **环境重置** - 一键重置Cursor环境
- 📧 **邮箱管理** - 统一管理邮箱凭据和验证码监控
- 🌐 **浏览器设置** - 配置Chrome浏览器路径
- 📊 **实时日志** - 实时显示操作状态和日志

## 安装依赖

在运行程序之前，请先安装所需的依赖包：

```bash
pip install -r requirements.txt
```

## 运行程序

有两种方式运行程序：

### 方式1：直接运行主程序
```bash
python 主程序.py
```

### 方式2：使用启动脚本
```bash
python run.py
```

## 主要改进

相比原版tkinter界面，PyQt6版本具有以下改进：

1. **更美观的界面设计**
   - 现代化的颜色主题
   - 圆角边框和阴影效果
   - 更好的字体和间距

2. **更好的用户体验**
   - 响应式布局
   - 平滑的动画效果
   - 更直观的状态指示

3. **增强的功能**
   - 更稳定的多线程处理
   - 更好的错误处理
   - 自动复制验证码到剪贴板

## 文件结构

```
pyqt6版本/
├── 主程序.py          # 主程序文件
├── 配置管理.py        # 配置管理模块
├── 邮箱管理.py        # 邮箱管理模块
├── 应用管理.py        # 应用管理模块
├── drivers/           # 浏览器驱动文件夹
│   └── chromedriver.exe
├── requirements.txt   # 依赖包列表
├── run.py            # 启动脚本
└── README.md         # 说明文档
```

## 注意事项

1. 确保已安装Python 3.8或更高版本
2. 首次运行前请安装所有依赖包
3. 程序会自动创建配置文件和日志
4. 支持Windows、macOS和Linux系统

## 故障排除

如果遇到问题，请检查：

1. **依赖包是否正确安装**
   ```bash
   pip list | grep PyQt6
   ```

2. **Python版本是否兼容**
   ```bash
   python --version
   ```

3. **是否有权限访问配置文件夹**

如果问题仍然存在，请查看控制台输出的错误信息。

## 使用指南

### 1. 一键操作工具
- **一键登录**: 自动执行登录流程
- **一键重置环境**: 重置Cursor的机器ID和相关配置

### 2. 邮箱管理中心
- **凭据设置**: 配置邮箱前缀和密码
- **随机邮箱**: 自动生成随机邮箱地址（每秒更新）
- **邮箱监控器**: 监控邮箱并自动获取验证码

### 3. 浏览器设置
- 配置Chrome浏览器的可执行文件路径
- 支持自动检测和手动选择

### 4. 操作日志
- 实时显示所有操作的状态和结果
- 自动滚动到最新消息
- 验证码自动复制到剪贴板

## 界面预览

PyQt6版本采用现代化设计：
- 🎨 清新的蓝色主题
- 📱 响应式布局
- 🔘 圆角按钮和边框
- 📊 实时状态指示器
- 🎯 直观的图标和表情符号

## 技术特性

- **多线程处理**: 避免界面冻结
- **异常处理**: 完善的错误处理机制
- **配置管理**: 自动保存和加载配置
- **跨平台支持**: Windows、macOS、Linux
- **现代化UI**: 基于PyQt6的美观界面

## 更新日志

### v2.0 (PyQt6版本)
- 🎨 全新的PyQt6界面设计
- 🚀 更好的性能和稳定性
- 📱 响应式布局适配
- 🔧 改进的配置管理
- 🎯 更直观的用户体验

## 开发者信息

本项目是原tkinter版本的PyQt6重写版本，保持了所有原有功能的同时，提供了更现代化的用户界面。

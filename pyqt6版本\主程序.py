import sys
import os
import traceback
import threading
import multiprocessing
import platform
import random
import pywinauto
from pywinauto.application import Application

from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QGridLayout, QLabel, QPushButton, 
                            QLineEdit, QTextEdit, QGroupBox, QFrame, 
                            QFileDialog, QMessageBox, QSizePolicy, QSpacerItem)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer, QSize
from PyQt6.QtGui import QFont, QPalette, QColor, QIcon, QPixmap

# 确保能正确导入模块
import sys
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from 配置管理 import get_config, save_config
from 应用管理 import run_full_reset_flow
from 邮箱管理 import email_manager, show_email_client, run_auto_login_flow

# === 工具模块内容 ===
# Define emoji constants
EMOJI = {
    "INFO": "ℹ️",
    "ERROR": "❌",
    "SUCCESS": "✅",
    "WARNING": "⚠️",
    "LOGIN": "🔑",
    "EMAIL": "📧",
    "USER": "👤",
    "COPY": "📋",
    "RESET": "🔄",
    "START": "▶️",
    "STOP": "⏹️"
}

def get_user_documents_path():
    """Get user documents path"""
    if platform.system() == "Windows":
        try:
            import winreg
            # 打开注册表
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, "Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Shell Folders") as key:
                # 获取 "Personal" 键的值，这指向用户的文档目录
                documents_path, _ = winreg.QueryValueEx(key, "Personal")
                return documents_path
        except Exception as e:
            # fallback
            return os.path.expanduser("~\\Documents")
    else:
        return os.path.expanduser("~/Documents")

def get_current_chrome_url():
    """
    Gets the URL of the active tab in Google Chrome.
    Returns the URL as a string or None if not found.
    """
    try:
        app = Application(backend="uia").connect(title_re=".*- Google Chrome", class_name="Chrome_WidgetWin_1", timeout=10)
        dlg = app.top_window()
        wrapper = dlg.child_window(auto_id="view_1000", control_type="ToolBar")
        address_bar = wrapper.child_window(control_type="Edit")

        if address_bar.exists():
            url = address_bar.get_value()
            return url, dlg # Return both URL and the window handle
        else:
            # Error: Could not find the address bar (silent for GUI app)
            return None, None

    except pywinauto.findwindows.ElementNotFoundError:
        print("Error: Google Chrome window not found.")
        return None, None
    except Exception as e:
        print(f"An unknown error occurred: {e}")
        return None, None

def get_default_driver_path(browser_type='chrome'):
    """Get default driver path for Chrome."""
    return get_default_chrome_driver_path()

def get_default_chrome_driver_path():
    """Get default Chrome driver path for Windows"""
    return os.path.join(os.path.dirname(os.path.abspath(__file__)), "drivers", "chromedriver.exe")

def get_default_browser_path():
    """Get default Chrome browser executable path for Windows"""
    # 1. Try to find via Windows Registry App Paths
    try:
        import winreg
        key_path = r"SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\chrome.exe"
        for root in [winreg.HKEY_LOCAL_MACHINE, winreg.HKEY_CURRENT_USER]:
            try:
                with winreg.OpenKey(root, key_path) as key:
                    path, _ = winreg.QueryValueEx(key, "")
                    if os.path.exists(path):
                        return path
            except FileNotFoundError:
                continue
    except (ImportError, OSError):
        pass

    # 2. Try to find in PATH environment variable
    try:
        import shutil
        path_from_env = shutil.which("chrome.exe")
        if path_from_env:
            return path_from_env
    except ImportError:
        pass

    # 3. Try user-specific installation path
    local_app_data = os.getenv('LOCALAPPDATA')
    if local_app_data:
        user_chrome_path = os.path.join(local_app_data, 'Google', 'Chrome', 'Application', 'chrome.exe')
        if os.path.exists(user_chrome_path):
            return user_chrome_path

    # 4. Fallback to system-wide installation path
    return r"C:\Program Files\Google\Chrome\Application\chrome.exe"

# Windows专用版本，移除了Linux相关代码

def get_random_wait_time(config, timing_key):
    """Get random wait time based on configuration timing settings

    Args:
        config (dict): Configuration dictionary containing timing settings
        timing_key (str): Key to look up in the timing settings

    Returns:
        float: Random wait time in seconds
    """
    try:
        # Get timing value from config
        timing = config.get('Timing', {}).get(timing_key)
        if not timing:
            # Default to 0.5-1.5 seconds if timing not found
            return random.uniform(0.5, 1.5)

        # Check if timing is a range (e.g., "0.5-1.5" or "0.5,1.5")
        if isinstance(timing, str):
            if '-' in timing:
                min_time, max_time = map(float, timing.split('-'))
            elif ',' in timing:
                min_time, max_time = map(float, timing.split(','))
            else:
                # Single value, use it as both min and max
                min_time = max_time = float(timing)
        else:
            # If timing is a number, use it as both min and max
            min_time = max_time = float(timing)

        return random.uniform(min_time, max_time)

    except (ValueError, TypeError, AttributeError):
        # Return default value if any error occurs
        return random.uniform(0.5, 1.5)

# === 现代化的颜色主题 ===
class ModernTheme:
    # 主色调
    PRIMARY = "#2563eb"
    PRIMARY_LIGHT = "#3b82f6"
    PRIMARY_DARK = "#1d4ed8"
    
    # 辅助色
    SECONDARY = "#10b981"
    ACCENT = "#f59e0b"
    DANGER = "#ef4444"
    WARNING = "#f59e0b"
    SUCCESS = "#10b981"
    
    # 背景色
    BACKGROUND = "#f8fafc"
    SURFACE = "#ffffff"
    CARD = "#ffffff"
    
    # 文字颜色
    TEXT_PRIMARY = "#1f2937"
    TEXT_SECONDARY = "#6b7280"
    TEXT_MUTED = "#9ca3af"
    
    # 边框颜色
    BORDER = "#e5e7eb"
    BORDER_LIGHT = "#f3f4f6"
    
    # 状态颜色
    INFO = "#3b82f6"
    ERROR = "#ef4444"

# === 自定义样式 ===
def get_modern_stylesheet():
    """获取现代化的样式表"""
    return f"""
    QMainWindow {{
        background-color: {ModernTheme.BACKGROUND};
        color: {ModernTheme.TEXT_PRIMARY};
        font-family: 'Microsoft YaHei UI', 'Segoe UI', sans-serif;
    }}
    
    QGroupBox {{
        font-weight: bold;
        font-size: 13px;
        border: 2px solid {ModernTheme.BORDER};
        border-radius: 8px;
        margin-top: 1ex;
        padding-top: 10px;
        background-color: {ModernTheme.SURFACE};
    }}
    
    QGroupBox::title {{
        subcontrol-origin: margin;
        left: 10px;
        padding: 0 8px 0 8px;
        color: {ModernTheme.TEXT_PRIMARY};
        background-color: {ModernTheme.SURFACE};
    }}
    
    QPushButton {{
        background-color: {ModernTheme.PRIMARY};
        color: white;
        border: none;
        border-radius: 6px;
        padding: 10px 18px;
        font-weight: bold;
        font-size: 12px;
        min-height: 25px;
    }}
    
    QPushButton:hover {{
        background-color: {ModernTheme.PRIMARY_LIGHT};
    }}
    
    QPushButton:pressed {{
        background-color: {ModernTheme.PRIMARY_DARK};
    }}
    
    QPushButton:disabled {{
        background-color: {ModernTheme.BORDER};
        color: {ModernTheme.TEXT_MUTED};
    }}
    
    QPushButton.success {{
        background-color: {ModernTheme.SUCCESS};
    }}
    
    QPushButton.success:hover {{
        background-color: #059669;
    }}
    
    QPushButton.warning {{
        background-color: {ModernTheme.WARNING};
    }}
    
    QPushButton.warning:hover {{
        background-color: #d97706;
    }}
    
    QPushButton.danger {{
        background-color: {ModernTheme.DANGER};
    }}
    
    QPushButton.danger:hover {{
        background-color: #dc2626;
    }}
    
    QLineEdit {{
        border: 2px solid {ModernTheme.BORDER};
        border-radius: 6px;
        padding: 10px 14px;
        font-size: 12px;
        background-color: {ModernTheme.SURFACE};
        selection-background-color: {ModernTheme.PRIMARY_LIGHT};
    }}
    
    QLineEdit:focus {{
        border-color: {ModernTheme.PRIMARY};
    }}
    
    QLineEdit:read-only {{
        background-color: {ModernTheme.BORDER_LIGHT};
        color: {ModernTheme.TEXT_SECONDARY};
    }}
    
    QTextEdit {{
        border: 2px solid {ModernTheme.BORDER};
        border-radius: 6px;
        padding: 10px;
        font-family: 'Consolas', 'Monaco', monospace;
        font-size: 20px;
        background-color: {ModernTheme.SURFACE};
        selection-background-color: {ModernTheme.PRIMARY_LIGHT};
    }}
    
    QLabel {{
        color: {ModernTheme.TEXT_PRIMARY};
        font-size: 12px;
    }}
    
    QLabel.title {{
        font-size: 20px;
        font-weight: bold;
        color: {ModernTheme.TEXT_PRIMARY};
    }}

    QLabel.subtitle {{
        font-size: 13px;
        color: {ModernTheme.TEXT_SECONDARY};
    }}
    
    QLabel.status-success {{
        color: {ModernTheme.SUCCESS};
        font-weight: bold;
    }}
    
    QLabel.status-warning {{
        color: {ModernTheme.WARNING};
        font-weight: bold;
    }}
    
    QLabel.status-error {{
        color: {ModernTheme.ERROR};
        font-weight: bold;
    }}
    """

# === 工作线程类 ===
class WorkerThread(QThread):
    """用于执行后台任务的工作线程"""
    status_update = pyqtSignal(str)
    finished = pyqtSignal()

    def __init__(self, task_func, *args, **kwargs):
        super().__init__()
        self.task_func = task_func
        self.args = args
        self.kwargs = kwargs

    def run(self):
        try:
            self.task_func(self.status_update.emit, *self.args, **self.kwargs)
        except Exception as e:
            self.status_update.emit(f"{EMOJI['ERROR']} 执行过程中发生错误: {e}")
            self.status_update.emit(traceback.format_exc())
        finally:
            self.finished.emit()

# === 自定义组件类 ===
class ModernGroupBox(QGroupBox):
    """现代化的分组框"""
    def __init__(self, title, parent=None):
        super().__init__(title, parent)
        self.setStyleSheet(f"""
            QGroupBox {{
                font-weight: bold;
                font-size: 11px;
                border: 2px solid {ModernTheme.BORDER};
                border-radius: 8px;
                margin-top: 1ex;
                padding-top: 15px;
                background-color: {ModernTheme.SURFACE};
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: {ModernTheme.TEXT_PRIMARY};
                background-color: {ModernTheme.SURFACE};
            }}
        """)

class ModernButton(QPushButton):
    """现代化的按钮"""
    def __init__(self, text, button_type="primary", parent=None):
        super().__init__(text, parent)
        self.button_type = button_type
        self.setMinimumHeight(40)
        self.setFont(QFont("Microsoft YaHei UI", 12, QFont.Weight.Bold))
        self.update_style()

    def update_style(self):
        if self.button_type == "primary":
            color = ModernTheme.PRIMARY
            hover_color = ModernTheme.PRIMARY_LIGHT
            pressed_color = ModernTheme.PRIMARY_DARK
        elif self.button_type == "success":
            color = ModernTheme.SUCCESS
            hover_color = "#059669"
            pressed_color = "#047857"
        elif self.button_type == "warning":
            color = ModernTheme.WARNING
            hover_color = "#d97706"
            pressed_color = "#b45309"
        elif self.button_type == "danger":
            color = ModernTheme.DANGER
            hover_color = "#dc2626"
            pressed_color = "#b91c1c"
        else:
            color = ModernTheme.PRIMARY
            hover_color = ModernTheme.PRIMARY_LIGHT
            pressed_color = ModernTheme.PRIMARY_DARK

        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 10px;
            }}
            QPushButton:hover {{
                background-color: {hover_color};
            }}
            QPushButton:pressed {{
                background-color: {pressed_color};
            }}
            QPushButton:disabled {{
                background-color: {ModernTheme.BORDER};
                color: {ModernTheme.TEXT_MUTED};
            }}
        """)

class ModernLineEdit(QLineEdit):
    """现代化的输入框"""
    def __init__(self, placeholder="", parent=None):
        super().__init__(parent)
        self.setPlaceholderText(placeholder)
        self.setMinimumHeight(40)
        self.setFont(QFont("Microsoft YaHei UI", 12))
        self.setStyleSheet(f"""
            QLineEdit {{
                border: 2px solid {ModernTheme.BORDER};
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 10px;
                background-color: {ModernTheme.SURFACE};
                selection-background-color: {ModernTheme.PRIMARY_LIGHT};
            }}
            QLineEdit:focus {{
                border-color: {ModernTheme.PRIMARY};
            }}
            QLineEdit:read-only {{
                background-color: {ModernTheme.BORDER_LIGHT};
                color: {ModernTheme.TEXT_SECONDARY};
            }}
        """)

class StatusTextEdit(QTextEdit):
    """状态显示文本框"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setReadOnly(True)
        self.setFont(QFont("Consolas", 32))  # 设置为32px
        self.setStyleSheet(f"""
            QTextEdit {{
                border: 2px solid {ModernTheme.BORDER};
                border-radius: 6px;
                padding: 8px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 32px;
                background-color: {ModernTheme.SURFACE};
                selection-background-color: {ModernTheme.PRIMARY_LIGHT};
            }}
        """)

    def append_status(self, message):
        """添加状态消息"""
        # 检查是否是验证码复制消息
        if message.startswith("COPY_AND_SHOW:"):
            try:
                # 提取验证码
                code = message.split("COPY_AND_SHOW:", 1)[1].strip()

                # 自动复制到剪贴板
                clipboard = QApplication.clipboard()
                clipboard.setText(code)

                # 显示友好的消息
                display_message = f"✅ 验证码已获取并自动复制到剪贴板: {code}"
                self.append(display_message)
            except Exception as e:
                # 如果处理验证码失败，显示原始消息
                self.append(message)
        else:
            # 普通消息的处理
            self.append(message)

        # 自动滚动到底部
        cursor = self.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.setTextCursor(cursor)
        self.ensureCursorVisible()

# === 功能组件类 ===
class AutoLoginFrame(ModernGroupBox):
    """一键操作组件"""
    login_clicked = pyqtSignal()
    reset_clicked = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(f"{EMOJI['LOGIN']} 一键操作工具", parent)
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout()

        # 描述标签
        desc_label = QLabel("快速执行登录和环境重置操作")
        desc_label.setStyleSheet(f"color: {ModernTheme.TEXT_SECONDARY}; font-size: 11px;")
        layout.addWidget(desc_label)

        # 按钮布局
        button_layout = QHBoxLayout()

        # 一键登录按钮
        self.login_button = ModernButton(f"{EMOJI['LOGIN']} 一键登录", "primary")
        self.login_button.clicked.connect(self.login_clicked.emit)
        button_layout.addWidget(self.login_button)

        # 一键重置环境按钮
        self.reset_button = ModernButton(f"{EMOJI['RESET']} 一键重置环境", "warning")
        self.reset_button.clicked.connect(self.reset_clicked.emit)
        button_layout.addWidget(self.reset_button)

        layout.addLayout(button_layout)
        self.setLayout(layout)

    def set_buttons_enabled(self, enabled):
        """设置按钮启用状态"""
        self.login_button.setEnabled(enabled)
        self.reset_button.setEnabled(enabled)

class EmailManagementFrame(ModernGroupBox):
    """邮箱管理组件"""
    save_credentials = pyqtSignal(str, str)  # prefix, password
    open_email_client = pyqtSignal()
    copy_email = pyqtSignal(str)

    def __init__(self, parent=None):
        super().__init__(f"{EMOJI['EMAIL']} 邮箱管理中心", parent)
        self.setup_ui()
        self.setup_timer()

    def setup_ui(self):
        layout = QVBoxLayout()

        # 描述标签
        desc_label = QLabel("统一管理邮箱凭据、随机邮箱生成和验证码监控")
        desc_label.setStyleSheet(f"color: {ModernTheme.TEXT_SECONDARY}; font-size: 11px;")
        layout.addWidget(desc_label)

        # 凭据设置区域
        cred_group = ModernGroupBox("📋 凭据设置")
        cred_layout = QGridLayout()

        # 邮箱前缀
        cred_layout.addWidget(QLabel("邮箱前缀:"), 0, 0)
        self.prefix_edit = ModernLineEdit("输入邮箱前缀")
        self.prefix_edit.setText(email_manager.email_prefix)
        cred_layout.addWidget(self.prefix_edit, 0, 1)

        format_label = QLabel("@2925.com")
        format_label.setStyleSheet(f"color: {ModernTheme.TEXT_SECONDARY}; font-size: 11px;")
        cred_layout.addWidget(format_label, 0, 2)

        # 邮箱密码
        cred_layout.addWidget(QLabel("邮箱密码:"), 1, 0)
        self.password_edit = ModernLineEdit("输入邮箱密码")
        self.password_edit.setText(email_manager.email_password)
        self.password_edit.setEchoMode(QLineEdit.EchoMode.Password)
        cred_layout.addWidget(self.password_edit, 1, 1)

        # 保存凭据按钮放在密码右边
        self.save_button = ModernButton(f"{EMOJI['SUCCESS']} 保存凭据", "success")
        self.save_button.clicked.connect(self.handle_save_credentials)
        cred_layout.addWidget(self.save_button, 1, 2)

        cred_group.setLayout(cred_layout)
        layout.addWidget(cred_group)

        # 随机邮箱区域
        random_group = ModernGroupBox("🎲 随机邮箱")
        random_layout = QGridLayout()

        # 状态指示器
        status_layout = QHBoxLayout()
        status_dot = QLabel("●")
        status_dot.setStyleSheet(f"color: {ModernTheme.SUCCESS}; font-weight: bold;")
        status_layout.addWidget(status_dot)

        info_label = QLabel("自动生成随机邮箱 (每秒更新)")
        info_label.setStyleSheet(f"color: {ModernTheme.TEXT_SECONDARY}; font-size: 11px;")
        status_layout.addWidget(info_label)
        status_layout.addStretch()

        random_layout.addLayout(status_layout, 0, 0, 1, 3)

        # 邮箱显示
        random_layout.addWidget(QLabel("邮箱:"), 1, 0)
        self.email_edit = ModernLineEdit()
        self.email_edit.setReadOnly(True)
        random_layout.addWidget(self.email_edit, 1, 1)

        self.copy_button = ModernButton(f"{EMOJI['COPY']} 复制", "success")
        self.copy_button.clicked.connect(self.handle_copy_email)
        random_layout.addWidget(self.copy_button, 1, 2)

        random_group.setLayout(random_layout)
        layout.addWidget(random_group)

        # 按钮区域
        button_layout = QHBoxLayout()

        self.email_client_button = ModernButton(f"{EMOJI['EMAIL']} 打开邮箱监控器", "primary")
        self.email_client_button.clicked.connect(self.open_email_client.emit)
        button_layout.addWidget(self.email_client_button)

        button_layout.addStretch()

        layout.addLayout(button_layout)
        self.setLayout(layout)

    def setup_timer(self):
        """设置定时器更新随机邮箱"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_random_email)
        self.timer.start(1000)  # 每秒更新
        self.update_random_email()  # 立即更新一次

    def update_random_email(self):
        """更新随机邮箱显示"""
        new_email = email_manager.generate_random_email()
        if new_email:
            self.email_edit.setText(new_email)
        else:
            self.email_edit.setText("")

    def handle_save_credentials(self):
        """处理保存凭据"""
        prefix = self.prefix_edit.text().strip()
        password = self.password_edit.text()
        self.save_credentials.emit(prefix, password)

    def handle_copy_email(self):
        """处理复制邮箱"""
        email = self.email_edit.text()
        if email:
            self.copy_email.emit(email)

    def set_buttons_enabled(self, enabled):
        """设置按钮启用状态"""
        self.email_client_button.setEnabled(enabled)

    def get_current_email(self):
        """获取当前显示的邮箱"""
        return self.email_edit.text()

    def get_current_password(self):
        """获取当前密码"""
        return self.password_edit.text()

class BrowserSettingsFrame(ModernGroupBox):
    """浏览器设置组件"""
    save_browser_path = pyqtSignal(str)

    def __init__(self, browser_path="", parent=None):
        super().__init__("🌐 浏览器设置", parent)
        self.browser_path = browser_path
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout()

        # 描述标签
        desc_label = QLabel("配置Chrome浏览器路径用于自动化操作")
        desc_label.setStyleSheet(f"color: {ModernTheme.TEXT_SECONDARY}; font-size: 11px;")
        layout.addWidget(desc_label)

        # 路径设置区域
        path_layout = QGridLayout()

        path_layout.addWidget(QLabel("Chrome路径:"), 0, 0)
        self.path_edit = ModernLineEdit("选择Chrome浏览器可执行文件")
        self.path_edit.setText(self.browser_path)
        path_layout.addWidget(self.path_edit, 0, 1)

        self.browse_button = ModernButton("📁 浏览...", "primary")
        self.browse_button.clicked.connect(self.handle_browse)
        path_layout.addWidget(self.browse_button, 0, 2)

        # 状态指示
        self.status_label = QLabel()
        self.update_status()
        path_layout.addWidget(self.status_label, 1, 1)

        self.save_button = ModernButton(f"{EMOJI['SUCCESS']} 保存路径", "success")
        self.save_button.clicked.connect(self.handle_save)
        path_layout.addWidget(self.save_button, 1, 2)

        layout.addLayout(path_layout)
        self.setLayout(layout)

    def update_status(self):
        """更新状态显示"""
        if self.browser_path and len(self.browser_path) > 0:
            self.status_label.setText("✅ 已配置")
            self.status_label.setStyleSheet(f"color: {ModernTheme.SUCCESS}; font-weight: bold; font-size: 11px;")
        else:
            self.status_label.setText("⚠️ 未配置")
            self.status_label.setStyleSheet(f"color: {ModernTheme.WARNING}; font-weight: bold; font-size: 11px;")

    def handle_browse(self):
        """处理浏览文件"""
        file_dialog = QFileDialog()
        file_path, _ = file_dialog.getOpenFileName(
            self, "选择Chrome浏览器可执行文件", "", "Executable files (*.exe);;All files (*.*)"
        )

        if file_path:
            self.path_edit.setText(file_path)
            self.browser_path = file_path
            self.update_status()

    def handle_save(self):
        """处理保存路径"""
        path = self.path_edit.text().strip()
        if path:
            self.save_browser_path.emit(path)
            self.browser_path = path
            self.update_status()

# === 主窗口类 ===
class MainWindow(QMainWindow):
    """主窗口类"""

    def __init__(self):
        super().__init__()
        self.config = None
        self.worker_thread = None
        self.init_config()
        self.setup_ui()
        self.setup_connections()

    def init_config(self):
        """初始化配置"""
        try:
            self.config = get_config()
            if not self.config:
                QMessageBox.critical(self, "致命错误", "无法加载或创建配置文件。应用程序将退出。")
                sys.exit(1)
        except Exception as e:
            QMessageBox.critical(self, "初始化错误", f"加载配置时出错: {e}")
            sys.exit(1)

    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("🔑 续杯工具 - Cursor管理助手")
        self.setMinimumSize(900, 700)
        self.resize(1000, 750)

        # 设置样式
        self.setStyleSheet(get_modern_stylesheet())

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # 标题区域
        title_layout = QHBoxLayout()

        title_label = QLabel("🔑 续杯工具")
        title_label.setStyleSheet("font-size: 22px; font-weight: bold; color: #1f2937;")
        title_layout.addWidget(title_label)

        subtitle_label = QLabel("Cursor管理助手 - 让续杯更简单")
        subtitle_label.setStyleSheet(f"font-size: 14px; color: {ModernTheme.TEXT_SECONDARY}; margin-left: 15px;")
        title_layout.addWidget(subtitle_label)

        title_layout.addStretch()
        main_layout.addLayout(title_layout)

        # 内容区域
        content_layout = QHBoxLayout()
        content_layout.setSpacing(20)

        # 左侧控制面板
        left_panel = QWidget()
        left_panel.setMaximumWidth(400)
        left_layout = QVBoxLayout(left_panel)
        left_layout.setSpacing(15)

        # 创建功能组件
        self.auto_login_frame = AutoLoginFrame()
        left_layout.addWidget(self.auto_login_frame)

        self.email_frame = EmailManagementFrame()
        left_layout.addWidget(self.email_frame)

        browser_path = self.config.get('Browser', 'chrome_path', fallback='')
        self.browser_frame = BrowserSettingsFrame(browser_path)
        left_layout.addWidget(self.browser_frame)

        left_layout.addStretch()
        content_layout.addWidget(left_panel)

        # 右侧状态显示区域
        right_panel = ModernGroupBox("📊 操作日志")
        right_layout = QVBoxLayout()

        self.status_text = StatusTextEdit()
        self.status_text.setMinimumHeight(400)
        right_layout.addWidget(self.status_text)

        right_panel.setLayout(right_layout)
        content_layout.addWidget(right_panel)

        main_layout.addLayout(content_layout)

        # 初始化状态
        self.status_text.append_status("准备就绪。请点击按钮开始。")

    def setup_connections(self):
        """设置信号连接"""
        # 一键操作连接
        self.auto_login_frame.login_clicked.connect(self.handle_auto_login)
        self.auto_login_frame.reset_clicked.connect(self.handle_reset_environment)

        # 邮箱管理连接
        self.email_frame.save_credentials.connect(self.handle_save_credentials)
        self.email_frame.open_email_client.connect(self.handle_open_email_client)
        self.email_frame.copy_email.connect(self.handle_copy_email)

        # 浏览器设置连接
        self.browser_frame.save_browser_path.connect(self.handle_save_browser_path)

    def update_status(self, message):
        """更新状态显示"""
        self.status_text.append_status(message)

    def clear_status_and_disable_buttons(self):
        """清空状态并禁用按钮"""
        self.auto_login_frame.set_buttons_enabled(False)
        self.email_frame.set_buttons_enabled(False)
        self.status_text.clear()

    def re_enable_buttons(self):
        """重新启用按钮"""
        self.auto_login_frame.set_buttons_enabled(True)
        self.email_frame.set_buttons_enabled(True)

    def handle_auto_login(self):
        """处理一键登录"""
        if self.worker_thread and self.worker_thread.isRunning():
            return

        def task(status_callback):
            password = self.email_frame.get_current_password()
            monitoring_email = email_manager.get_monitoring_email()
            login_email = self.email_frame.get_current_email()

            run_auto_login_flow(monitoring_email, login_email, password, status_callback)

        self.clear_status_and_disable_buttons()
        self.worker_thread = WorkerThread(task)
        self.worker_thread.status_update.connect(self.update_status)
        self.worker_thread.finished.connect(self.re_enable_buttons)
        self.worker_thread.start()

    def handle_reset_environment(self):
        """处理环境重置"""
        if self.worker_thread and self.worker_thread.isRunning():
            return

        def task(status_callback):
            run_full_reset_flow(status_callback)

        self.clear_status_and_disable_buttons()
        self.worker_thread = WorkerThread(task)
        self.worker_thread.status_update.connect(self.update_status)
        self.worker_thread.finished.connect(self.re_enable_buttons)
        self.worker_thread.start()

    def handle_save_credentials(self, prefix, password):
        """处理保存凭据"""
        if email_manager.save_credentials(prefix, password):
            self.update_status(f"{EMOJI['SUCCESS']} 凭据已保存。")
        else:
            self.update_status(f"{EMOJI['ERROR']} 保存凭据失败。")

    def handle_open_email_client(self):
        """处理打开邮箱客户端"""
        try:
            current_password = self.email_frame.get_current_password()
            show_email_client(self, current_password)
            self.update_status(f"{EMOJI['SUCCESS']} 邮箱客户端已打开")
        except Exception as e:
            self.update_status(f"{EMOJI['ERROR']} 打开邮箱客户端失败: {e}")

    def handle_copy_email(self, email):
        """处理复制邮箱"""
        clipboard = QApplication.clipboard()
        clipboard.setText(email)
        self.update_status(f"{EMOJI['COPY']} 邮箱已复制到剪贴板: {email}")

    def handle_save_browser_path(self, path):
        """处理保存浏览器路径"""
        if not path or not os.path.exists(path):
            self.update_status(f"{EMOJI['ERROR']} 路径无效或文件不存在: {path}")
            return

        self.config.set('Browser', 'chrome_path', path)

        if save_config(self.config):
            self.update_status(f"{EMOJI['SUCCESS']} 浏览器路径已保存。")
        else:
            self.update_status(f"{EMOJI['ERROR']} 保存浏览器路径失败。")

    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.worker_thread and self.worker_thread.isRunning():
            self.worker_thread.terminate()
            self.worker_thread.wait()
        event.accept()

# === 全局异常处理 ===
def setup_global_exception_handler():
    """设置全局异常处理器"""
    def handle_exception(exc_type, exc_value, exc_traceback):
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return

        try:
            QMessageBox.critical(
                None,
                "Fatal Error",
                f"A critical error occurred and the application must close.\n\n"
                f"Error: {exc_value}"
            )
        except Exception:
            pass

    sys.excepthook = handle_exception

# === 主函数 ===
def create_gui():
    """创建并运行PyQt6 GUI"""
    app = QApplication(sys.argv)

    # 设置应用程序属性
    app.setApplicationName("续杯工具")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("Cursor管理助手")

    # 设置字体 - 增大字体大小
    font = QFont("Microsoft YaHei UI", 11)
    app.setFont(font)

    # 创建主窗口
    window = MainWindow()
    window.show()

    return app.exec()

if __name__ == "__main__":
    # For multiprocessing to work correctly when packaged
    multiprocessing.freeze_support()

    # 设置全局异常处理
    setup_global_exception_handler()

    # 运行应用程序
    sys.exit(create_gui())

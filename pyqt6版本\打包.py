#!/usr/bin/env python3
"""
续杯工具 - PyQt6版本打包脚本
自动打包程序并清理临时文件
"""

import os
import sys
import subprocess
import shutil
import time
from pathlib import Path

def print_header():
    """打印标题"""
    print("=" * 50)
    print("续杯工具 - PyQt6版本打包脚本")
    print("=" * 50)
    print()

def check_python():
    """检查Python环境"""
    print("🔍 正在检查Python环境...")
    try:
        version = sys.version_info
        if version.major < 3 or (version.major == 3 and version.minor < 8):
            print("❌ 错误: 需要Python 3.8或更高版本")
            return False
        print(f"✅ Python {version.major}.{version.minor}.{version.micro}")
        return True
    except Exception as e:
        print(f"❌ 检查Python环境失败: {e}")
        return False

def install_pyinstaller():
    """安装PyInstaller"""
    print("🔍 正在检查PyInstaller...")
    try:
        import PyInstaller
        print("✅ PyInstaller已安装")
        return True
    except ImportError:
        print("📦 正在安装PyInstaller...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"],
                         check=True, capture_output=True)
            print("✅ PyInstaller安装成功")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ PyInstaller安装失败: {e}")
            return False

def install_dependencies():
    """安装依赖包"""
    print("📦 正在检查依赖包...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"],
                     check=True, capture_output=True)
        print("✅ 依赖包检查完成")
        return True
    except subprocess.CalledProcessError as e:
        print("⚠️ 警告: 部分依赖包安装失败，但继续打包...")
        return True

def build_executable():
    """打包可执行文件"""
    print("\n🚀 开始打包程序...")
    print("这可能需要几分钟时间，请耐心等待...")
    print()

    try:
        # 检查spec文件是否存在，如果不存在则使用直接命令
        if os.path.exists("build.spec"):
            print("📄 使用build.spec配置文件打包...")
            cmd = [sys.executable, "-m", "PyInstaller", "build.spec"]
        else:
            print("📄 使用默认配置打包...")
            # 确保使用绝对路径
            main_script = os.path.abspath("主程序.py")
            drivers_path = os.path.abspath("drivers")

            cmd = [
                sys.executable, "-m", "PyInstaller",
                "--onefile",
                "--windowed",
                "--name=续杯工具",
                f"--add-data={drivers_path};drivers",
                "--add-data=requirements.txt;.",
                "--add-data=README.md;.",
                "--add-data=快速开始.txt;.",
                main_script
            ]

        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            print("✅ 打包成功完成！")
            return True
        else:
            print("❌ 打包失败:")
            print(result.stderr)
            return False

    except Exception as e:
        print(f"❌ 打包过程中发生错误: {e}")
        return False

def cleanup_files():
    """清理打包产生的临时文件"""
    print("\n🧹 正在清理临时文件...")

    cleanup_items = [
        "build",           # 构建临时目录
        "__pycache__",     # Python缓存
        "*.pyc",           # 编译的Python文件
        "*.pyo",           # 优化的Python文件
        "*.spec~",         # spec文件备份
        "build.spec",      # spec文件
        "续杯工具.spec",    # 自动生成的spec文件
    ]

    cleaned_count = 0

    for item in cleanup_items:
        if item == "build":
            if os.path.exists("build"):
                try:
                    shutil.rmtree("build")
                    print(f"   ✅ 删除 build/ 目录")
                    cleaned_count += 1
                except Exception as e:
                    print(f"   ⚠️ 删除 build/ 失败: {e}")

        elif item == "__pycache__":
            # 递归删除所有__pycache__目录
            for root, dirs, _ in os.walk("."):
                if "__pycache__" in dirs:
                    cache_path = os.path.join(root, "__pycache__")
                    try:
                        shutil.rmtree(cache_path)
                        print(f"   ✅ 删除 {cache_path}")
                        cleaned_count += 1
                    except Exception as e:
                        print(f"   ⚠️ 删除 {cache_path} 失败: {e}")

        elif item.startswith("*."):
            # 删除匹配的文件
            import glob
            pattern = item
            for file_path in glob.glob(pattern):
                try:
                    os.remove(file_path)
                    print(f"   ✅ 删除 {file_path}")
                    cleaned_count += 1
                except Exception as e:
                    print(f"   ⚠️ 删除 {file_path} 失败: {e}")

        else:
            # 删除单个文件
            if os.path.exists(item):
                try:
                    os.remove(item)
                    print(f"   ✅ 删除 {item}")
                    cleaned_count += 1
                except Exception as e:
                    print(f"   ⚠️ 删除 {item} 失败: {e}")

    print(f"🧹 清理完成，共删除 {cleaned_count} 个项目")

def show_results():
    """显示打包结果"""
    print("\n" + "=" * 50)
    print("🎉 打包完成！")
    print("=" * 50)

    exe_path = Path("dist/续杯工具.exe")
    if exe_path.exists():
        size_mb = exe_path.stat().st_size / (1024 * 1024)
        print(f"📁 可执行文件: {exe_path}")
        print(f"📊 文件大小: {size_mb:.1f} MB")
    else:
        print("⚠️ 未找到可执行文件，请检查打包过程")

    print("\n📋 注意事项:")
    print("1. 首次运行可能需要较长时间")
    print("2. 确保目标机器有Chrome浏览器")
    print("3. 程序会自动创建配置文件")
    print("4. 可以将整个dist文件夹分发给用户")

def main():
    """主函数"""
    print_header()

    # 确保在正确的目录中运行
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    print(f"📁 工作目录: {script_dir}")

    # 检查必要文件是否存在
    required_files = ["主程序.py", "配置管理.py", "邮箱管理.py", "应用管理.py"]
    missing_files = [f for f in required_files if not os.path.exists(f)]

    if missing_files:
        print(f"❌ 错误: 缺少必要文件: {', '.join(missing_files)}")
        print("请确保在pyqt6版本目录中运行此脚本")
        input("按回车键退出...")
        return False

    print("✅ 所有必要文件都存在")

    # 检查环境
    if not check_python():
        input("按回车键退出...")
        return False

    if not install_pyinstaller():
        input("按回车键退出...")
        return False

    if not install_dependencies():
        input("按回车键退出...")
        return False

    # 打包
    if not build_executable():
        input("按回车键退出...")
        return False

    # 清理
    cleanup_files()

    # 显示结果
    show_results()

    print("\n✅ 所有操作完成！")
    input("按回车键退出...")
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 发生未知错误: {e}")
        input("按回车键退出...")

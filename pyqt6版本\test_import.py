#!/usr/bin/env python3
"""
测试PyQt6版本的导入是否正常
"""

import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_imports():
    """测试所有模块的导入"""
    print("测试PyQt6版本模块导入...")
    
    try:
        print("1. 测试PyQt6...")
        from PyQt6.QtWidgets import QApplication
        print("   ✅ PyQt6导入成功")
    except ImportError as e:
        print(f"   ❌ PyQt6导入失败: {e}")
        return False
    
    try:
        print("2. 测试配置管理...")
        from 配置管理 import get_config
        print("   ✅ 配置管理导入成功")
    except ImportError as e:
        print(f"   ❌ 配置管理导入失败: {e}")
        return False
    
    try:
        print("3. 测试应用管理...")
        from 应用管理 import run_full_reset_flow
        print("   ✅ 应用管理导入成功")
    except ImportError as e:
        print(f"   ❌ 应用管理导入失败: {e}")
        return False
    
    try:
        print("4. 测试邮箱管理...")
        from 邮箱管理 import email_manager
        print("   ✅ 邮箱管理导入成功")
    except ImportError as e:
        print(f"   ❌ 邮箱管理导入失败: {e}")
        return False
    
    try:
        print("5. 测试主程序...")
        from 主程序 import MainWindow, ModernTheme
        print("   ✅ 主程序导入成功")
    except ImportError as e:
        print(f"   ❌ 主程序导入失败: {e}")
        return False
    
    print("\n🎉 所有模块导入测试通过！")
    return True

def test_config():
    """测试配置系统"""
    print("\n测试配置系统...")
    
    try:
        from 配置管理 import get_config
        config = get_config()
        if config:
            print("   ✅ 配置文件加载成功")
            return True
        else:
            print("   ❌ 配置文件加载失败")
            return False
    except Exception as e:
        print(f"   ❌ 配置系统测试失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("续杯工具 PyQt6版本 - 导入测试")
    print("=" * 50)
    
    # 测试导入
    import_success = test_imports()
    
    if import_success:
        # 测试配置
        config_success = test_config()
        
        if config_success:
            print("\n✅ 所有测试通过！程序应该可以正常运行。")
            print("\n运行程序:")
            print("python 主程序.py")
            print("或")
            print("python run.py")
        else:
            print("\n⚠️ 配置系统测试失败，但程序可能仍可运行。")
    else:
        print("\n❌ 导入测试失败，请检查依赖包是否正确安装。")
        print("\n安装依赖包:")
        print("pip install -r requirements.txt")
    
    print("\n" + "=" * 50)
